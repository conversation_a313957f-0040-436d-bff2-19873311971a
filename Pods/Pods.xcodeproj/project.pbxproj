// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		00DE7AE1D2A7B7F94F93458DB7754809 /* IQPreviousNextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 270C1B5EA640DB553AE42470C7798AEC /* IQPreviousNextView.swift */; };
		0301973EE3FDE2CFB75C3D6445F25992 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ED3234428715ED881C945B13B58F9250 /* Foundation.framework */; };
		064D909CD827405E8DCC309DB1B7775A /* ConstraintLayoutSupportDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = F00CA9C87938AE674D9C17133FD3E262 /* ConstraintLayoutSupportDSL.swift */; };
		0895381789B493294369A187CF16097B /* AAGradientColor+RadialGradient.swift in Sources */ = {isa = PBXBuildFile; fileRef = B7408994E8B02F05DD4E5D2D1EA25EC2 /* AAGradientColor+RadialGradient.swift */; };
		09E1F569A93FAD4B9149E30B9301F44A /* ConstraintPriority.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6C48CBC3A65804D80F45F4F857055A7F /* ConstraintPriority.swift */; };
		0C7C78E9CA8DC47092FAF58E9FCA2A13 /* AAPlotBandsElement.swift in Sources */ = {isa = PBXBuildFile; fileRef = 069A551D0B6200D07EB296DDC229FC6E /* AAPlotBandsElement.swift */; };
		0CA7A132ABE7018DE9295456732F38BB /* ConstraintAttributes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5471F27C9FC5A9F05558B10F1837DFA9 /* ConstraintAttributes.swift */; };
		0DDF4216E3E48EF14B994DEAF828EEA2 /* IQKeyboardManager+Internal.swift in Sources */ = {isa = PBXBuildFile; fileRef = 52950D23954B62BA8DEDC65AA88B2908 /* IQKeyboardManager+Internal.swift */; };
		0DE5DB9C6227B3416778D8417DD95EA9 /* ConstraintView+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = E03F894CAA5A9D681C103609D6AB734B /* ConstraintView+Extensions.swift */; };
		0F2BF86610A292C81CEA284234E49055 /* AAChartView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6EDB1CB2DE510F33C8DA683776CE5D2B /* AAChartView.swift */; };
		1083DD9C24614A83BD0A843008DF366C /* AAStates.swift in Sources */ = {isa = PBXBuildFile; fileRef = 71BB6C68136D7C7D29FDCAE97FA070DD /* AAStates.swift */; };
		1194E62AA3F6F506799B1A43B16942B5 /* ConstraintDirectionalInsets.swift in Sources */ = {isa = PBXBuildFile; fileRef = C287C539748BDC4A6E64E459102794E5 /* ConstraintDirectionalInsets.swift */; };
		11B67C495111E3AA200F8E6852A73A55 /* IQKeyboardManagerSwift-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = A2302A35710DB980F5D0CD9A7D204ABF /* IQKeyboardManagerSwift-dummy.m */; };
		139F8FB4B5A5FA7A297976A723CDCC9A /* IQUITextFieldView+Additions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 20A2A456A86322ED11F6C9351576FC09 /* IQUITextFieldView+Additions.swift */; };
		17850B3BCF62F3E9C80608BF8B16291A /* IQTextFieldViewListener.swift in Sources */ = {isa = PBXBuildFile; fileRef = B18E9E42F6DB3D1A4662C0E6B0948B20 /* IQTextFieldViewListener.swift */; };
		1ACC61B072F4BB20E73C2F462A7E16FB /* UIImage+NextPrevious.swift in Sources */ = {isa = PBXBuildFile; fileRef = 931B256AE49B31B2919957A56690B478 /* UIImage+NextPrevious.swift */; };
		205EB01AED14BB574DD54EAFE26E4786 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ED3234428715ED881C945B13B58F9250 /* Foundation.framework */; };
		21F8C522C6567324FEFF24831E3F4FA4 /* AAChartView+API.swift in Sources */ = {isa = PBXBuildFile; fileRef = C6E2D909636C86AC957D5CE04F4128D3 /* AAChartView+API.swift */; };
		23E7FD4F69645294729EB3839D524607 /* AAGradientColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1BD61D84A1CFA78D05835E84979EF817 /* AAGradientColor.swift */; };
		261F1743BA33D3065C9FD7D3D7D85BED /* AAButtonTheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = FC90D5D73117BFE6B72AC241483DD5BB /* AAButtonTheme.swift */; };
		28CF7A42811BA9B634621316DCAFFCC5 /* AAPlotOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4FFD26A6443E6C39B3C83BA581406A1D /* AAPlotOptions.swift */; };
		2B2EB369550CE92CEEFCBFD3D32B8A3F /* ConstraintInsetTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 459A45FEBCEBE298BEE465D480035D7A /* ConstraintInsetTarget.swift */; };
		2C1207AB554753DD987E471DEA8E9DED /* AAAnimation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D9915F67AD1CD0832E475399DFDD1DE /* AAAnimation.swift */; };
		2CE3EDDFC3568BED3EBB743D36BD0A69 /* AAPie.swift in Sources */ = {isa = PBXBuildFile; fileRef = C5C0857160FC1F5CC0EDD7CE22DCFE87 /* AAPie.swift */; };
		2F4337F8364DE531ADD9281A9F7CBA9F /* AAPackedbubble.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4AB6104F539C1671A460348EE54AB0A4 /* AAPackedbubble.swift */; };
		312C7FD0CFDC0D70C789F4107C37D627 /* AALegend.swift in Sources */ = {isa = PBXBuildFile; fileRef = 483D6FF787DF666ABAF5013EFAD42A13 /* AALegend.swift */; };
		353180A0F0DC4B61F2BDED206473D6F9 /* AADataLabels.swift in Sources */ = {isa = PBXBuildFile; fileRef = F5A02C54472B6F3EC32A0160D8A57200 /* AADataLabels.swift */; };
		356F2FE4EDACF39DAFA756E537ECD06B /* AAScatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6EF0BE6555E9CAE20A713625296145A7 /* AAScatter.swift */; };
		3577F172FA68CBAE47CFEE6FE25C5404 /* ConstraintOffsetTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 75C6F3B3F08C44B224062AFCA5FCB1FC /* ConstraintOffsetTarget.swift */; };
		3A3D2697E544C969F5C736F404A1BB66 /* IQKeyboardManagerSwift-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 32AF54A9F4796CD636751A693B4281C9 /* IQKeyboardManagerSwift-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3CBB581828D3D4237FF0E318F3457FC9 /* IQBarButtonItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = E176AFB682B78E2894FDAB3C089D03F9 /* IQBarButtonItem.swift */; };
		3D3B646B4988314275B40E97BEB16C7F /* ConstraintLayoutGuideDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 605546073AF43F60F673269621B44E0D /* ConstraintLayoutGuideDSL.swift */; };
		3E52BAFA9AECEF850B67B1E7C32650BF /* AACrosshair.swift in Sources */ = {isa = PBXBuildFile; fileRef = E1F16131E80C86699226A8045B09BC93 /* AACrosshair.swift */; };
		3EEB67A2DC733DD8677E443D43BA9A5A /* IQKeyboardInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7245C51D5B2203AE0EFC04500217F758 /* IQKeyboardInfo.swift */; };
		3EF4E67F7853C9EC6078213CACC5EAE5 /* Pods-ChargeSpot-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 4BCF6A1012C98D75A6011FEC7D445536 /* Pods-ChargeSpot-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		45459AF4B2FD64DD66FBA5889A811D9E /* AAInfographics-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 24CF5A669371F7CD04FDEEC4CC2031D7 /* AAInfographics-dummy.m */; };
		45B3D0DA40BE97096DE3CDECE622585E /* IQKeyboardManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 735F64F42825B664FB6A870FB8A0D27F /* IQKeyboardManager.swift */; };
		48DF918490C82FE2849F2058784FCB79 /* AAPane.swift in Sources */ = {isa = PBXBuildFile; fileRef = E00481C8F4DAA1927620296FC1202DF8 /* AAPane.swift */; };
		4A84D7C091F340B22AAD8B88C1522160 /* AASeries.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5F3514E3F21055E195A5942DA3F38AD5 /* AASeries.swift */; };
		4F4DEB687C0E4834A5B291DEE0651D6A /* ConstraintMaker.swift in Sources */ = {isa = PBXBuildFile; fileRef = 550887E8DA7F780888F21EE1C8CAE165 /* ConstraintMaker.swift */; };
		******************************** /* AABubbleLegend.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******************************** /* AABubbleLegend.swift */; };
		513FF454FF96C8751781104F7A651A9B /* AATooltip.swift in Sources */ = {isa = PBXBuildFile; fileRef = 063F91C01308FA71ED4E8B32BCB63DB4 /* AATooltip.swift */; };
		54D89FF142C65EDD590F8651A0CFCB6E /* IQTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C89B730732A92E19E9E9B580E5A5B74 /* IQTextView.swift */; };
		568C235842D9191E16FFB6BCC2DBF6ED /* AASubtitle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9106F9AB42732BB5BF87F44EE52534AA /* AASubtitle.swift */; };
		57C4F6EFB30DDD14E960AC2D6B34F904 /* SnapKit-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = FA7712C6E6B40A90C46A2CC450F20E26 /* SnapKit-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		57EA452F77A51C3654DBC92A9A2B771A /* IQUIScrollView+Additions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A69D4E881ED3E3E0FB12176B8A7FF999 /* IQUIScrollView+Additions.swift */; };
		5922A6A0AE7152CF436356B3556F1835 /* ConstraintItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = F0AB94E52BD76051BE55DFDB83A9F8C1 /* ConstraintItem.swift */; };
		595079712F4E905BEEC8C8EAA167EA03 /* IQToolbarConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = B979BCAFEA5C9EC623AB08B15FFA21FD /* IQToolbarConfiguration.swift */; };
		59E13CB1013047AB75A9B464B1D82C2F /* IQKeyboardConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = F755C70D65DD35629230631996D38E4F /* IQKeyboardConfiguration.swift */; };
		59F34874DA4ABB2F5C4E09EA6865936B /* ConstraintLayoutGuide.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63AF321CEA03A02B1345A22B88B465CE /* ConstraintLayoutGuide.swift */; };
		5CA32A2520B7868CF67B02C1C830F5A8 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A912741BDA04F14F5D091E6DD6B95F90 /* QuartzCore.framework */; };
		5D269576532F504523EAA39DDB1557F4 /* IQKeyboardManager+Debug.swift in Sources */ = {isa = PBXBuildFile; fileRef = A0CFC24F45E44BA1194FA744417F3A8D /* IQKeyboardManager+Debug.swift */; };
		663F1294E62250BD0DF7888F2173B0D6 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9CE1B6592A29ADBBC5FB53143C4D4955 /* CoreGraphics.framework */; };
		67FE77018A04D8691A906DECD6CFFCA2 /* IQKeyboardReturnKeyHandler+TextViewDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD988279CBF80937BF54DECF9BD39BEF /* IQKeyboardReturnKeyHandler+TextViewDelegate.swift */; };
		68D7B750E799100042584648B0634DDD /* IQTitleBarButtonItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4FD5C6E1EA88852FEF8628F34A4C0BCA /* IQTitleBarButtonItem.swift */; };
		6C08C17E6D1DB2B4D1D0669F59F09368 /* IQKeyboardManagerCompatible.swift in Sources */ = {isa = PBXBuildFile; fileRef = E173C42A0A833109802BF258B4984666 /* IQKeyboardManagerCompatible.swift */; };
		6CBE440BE36788B5E56E7C6124B2455A /* AALabel.swift in Sources */ = {isa = PBXBuildFile; fileRef = D65EF91816145FD72DA8295640A52596 /* AALabel.swift */; };
		6DD19D888A917365E4C36AFE31FA2C27 /* IQKeyboardManager+Position.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B44D1146246358045F9241021CF368C /* IQKeyboardManager+Position.swift */; };
		6E39129FC8643A70C276801FEF4C280D /* Constraint.swift in Sources */ = {isa = PBXBuildFile; fileRef = A06E7979FF01D8CE948AAEA07223CC27 /* Constraint.swift */; };
		70364D5528E3495AA5DE40030C0852B8 /* IQUITableView+Additions.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF330D9FBA848850DA54AB516A4C6A10 /* IQUITableView+Additions.swift */; };
		707F5D2C0DD51CE01C4BCD8ACAFDE4F4 /* AAChartViewPluginProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = ACC2B8C0FDB7AF11990DCF1E194190C7 /* AAChartViewPluginProvider.swift */; };
		70A2FE54635EEBD101D5D79B01609C4D /* AAAxis.swift in Sources */ = {isa = PBXBuildFile; fileRef = 994CAF88FFE5AF0B5EBA4224FD47E4A1 /* AAAxis.swift */; };
		72E7B39F6D1F97F30C08E04D211D304E /* IQKeyboardReturnKeyHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = F6F75BA63EFB3F9CCA59C17FC7B80582 /* IQKeyboardReturnKeyHandler.swift */; };
		7359EA4AA9D0841330E2F010AD8F25A5 /* AAStyle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1C3D0C9E374DD4F56223D64BBE9D67C3 /* AAStyle.swift */; };
		742CBD9F365744155B66ADE0EC6CF94B /* IQToolbarPlaceholderConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B39734B699CD68B73BAE285F10E54CA /* IQToolbarPlaceholderConfiguration.swift */; };
		743A1D7C259156C99A14E4B1FD01A52C /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 69301F4E598400CA4CB88505EAF858B1 /* UIKit.framework */; };
		7713C42CE43548B685917AA35DC638E9 /* ProjectBundlePathLoader.swift in Sources */ = {isa = PBXBuildFile; fileRef = F5CB8ACCF4F8D71BCE60B87052B0B347 /* ProjectBundlePathLoader.swift */; };
		7AF516B98D45391B909D507D0244104C /* ConstraintDescription.swift in Sources */ = {isa = PBXBuildFile; fileRef = E213D89E47AE3679ADB1CB0AAEF3A8E1 /* ConstraintDescription.swift */; };
		7C7D4E1F438258C4363547D5CD7826CE /* AAMarker.swift in Sources */ = {isa = PBXBuildFile; fileRef = E53B5E444D65BD6CFA1F562F80DE610B /* AAMarker.swift */; };
		7D42390CDB4FA147504B03DA2A174A0C /* ConstraintViewDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8989A59ADC6A439686C9BE31BCC827A8 /* ConstraintViewDSL.swift */; };
		7F9F8BFE4A612F1EE0C0B1688185ED35 /* AAOptions.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4E28C64704DBDA820F361F088CFAFE6 /* AAOptions.swift */; };
		808A3D2BE172D93F862574233C07722C /* Pods-ChargeSpot-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 10AAA8D774CADF4B1B7851B0532531F8 /* Pods-ChargeSpot-dummy.m */; };
		80E37F3FDB349D19B5DD572479F75315 /* AASeriesElement.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04A91AF5C89A203A66943E259030D56E /* AASeriesElement.swift */; };
		83A2615D5A90DC223EA13831DFA4CE13 /* IQUIView+IQKeyboardToolbarDeprecated.swift in Sources */ = {isa = PBXBuildFile; fileRef = C665C3EEBF85BE7F61EF12436F008782 /* IQUIView+IQKeyboardToolbarDeprecated.swift */; };
		851B0A1B6750B5464D9300F8DE62CAF2 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ED3234428715ED881C945B13B58F9250 /* Foundation.framework */; };
		858D141CDA8B7F21BCBE5CD8476F0F53 /* AAPlotLinesElement.swift in Sources */ = {isa = PBXBuildFile; fileRef = 419CB0F7F66FEAF40DD0EADF5509659A /* AAPlotLinesElement.swift */; };
		868A9F524A7985BDA1EA124D9BF4CA63 /* ConstraintDSL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1BB2E7DC6F46623C46A99E439E577A39 /* ConstraintDSL.swift */; };
		86CAB01D950C8BC35EDE0BDC01A2500B /* ConstraintView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5CD1FC29720287740820EF2F7613FDCE /* ConstraintView.swift */; };
		883EDEE1C699497CF2A77C3B8A32A790 /* ConstraintMultiplierTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2AB1B027EC52C740BC3F056B0F1F17D7 /* ConstraintMultiplierTarget.swift */; };
		8BABA32F7B94A25D8E9208C0A8D90B2E /* ConstraintMakerRelatable+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 06C2F42F525AFD9989167107534D0413 /* ConstraintMakerRelatable+Extensions.swift */; };
		8ECCBA8E91C632E4F56D0D7F40F135F9 /* IQKeyboardManager+UITextFieldViewNotification.swift in Sources */ = {isa = PBXBuildFile; fileRef = FCFD46E8DA59B6DB18C6D8F420E9E5E7 /* IQKeyboardManager+UITextFieldViewNotification.swift */; };
		90005505FB933A3047BE185F63A99FB3 /* AAChart.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C515E0AC897C731289C54184A98A795 /* AAChart.swift */; };
		903685875CBD28FE7FBF09128948638E /* AAScrollablePlotArea.swift in Sources */ = {isa = PBXBuildFile; fileRef = B8261D73E5486EBCF552FC35653D39F4 /* AAScrollablePlotArea.swift */; };
		923CA1FDC87DDEA88E02067BECD83C78 /* AABoxplot.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0916C40CF3EC3AB0C1049A7CC982EF11 /* AABoxplot.swift */; };
		******************************** /* AAInfographics-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = D03D93D9AD4368659CCBFE34345185F8 /* AAInfographics-umbrella.h */; settings = {ATTRIBUTES = (Public, ); }; };
		9471927F8AAA87EA82DBEC2880E23A1D /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = ED3234428715ED881C945B13B58F9250 /* Foundation.framework */; };
		990BE6EFC195B60FCFD4BE108238E6D0 /* AAChartModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = E94478C0EE33FA6880675BA62034B30B /* AAChartModel.swift */; };
		9963434D8659BEAE9B61E4E9E2A5C412 /* AAYAxis.swift in Sources */ = {isa = PBXBuildFile; fileRef = 77BB3CE04DFFC883C9616BAABFAAD824 /* AAYAxis.swift */; };
		99B2134E59C2BAFCFD0EAD4E508007A1 /* AAXAxis.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5061C5644507E3A08CCA27AE872C6CF9 /* AAXAxis.swift */; };
		9A578D9CD4BEC7C1168A1D9820DE40A8 /* IQKeyboardManager+ToolbarActions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4A9D8A008A2704939DA1E21BC5DEC047 /* IQKeyboardManager+ToolbarActions.swift */; };
		9AD8B9EFD0F8D7B60A5DC4359601ED60 /* MainActor+AssumeIsolated.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D2C2C81E06765F6672A624A00006108 /* MainActor+AssumeIsolated.swift */; };
		9BBD05F55C106CC86C80DA2DA985C2B3 /* AASerializable.swift in Sources */ = {isa = PBXBuildFile; fileRef = C7773C7D7B5BF5AC5B5FEBE20BAB79AA /* AASerializable.swift */; };
		9E0045B41BFE697DB4ADE151228024D2 /* SnapKit-SnapKit_Privacy in Resources */ = {isa = PBXBuildFile; fileRef = B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */; };
		A267DD79A7AAD3036D940CDE6249255B /* IQNSArray+Sort.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08DB70849B83F30C9141FEF7617A6B26 /* IQNSArray+Sort.swift */; };
		A9D7688EFF99BED99A406255EBFCCC6D /* IQUIViewController+Additions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 449692FFD3124EC6C5FD624C59F126ED /* IQUIViewController+Additions.swift */; };
		AABEF13464BA7F4621BD94736C1D057C /* ConstraintMakerPrioritizable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5AEB23950B83A8D1002278B4EC108F39 /* ConstraintMakerPrioritizable.swift */; };
		ABCE13FE54D5DEE5CD02838201676A92 /* IQTextFieldViewInfoModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1BE29506F66C0B2EB180E5D42A992AA0 /* IQTextFieldViewInfoModel.swift */; };
		AE224EDB6D044C0FE86B086E950FC2F9 /* Debugging.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23201A67174F0451961F2B74F5414EC8 /* Debugging.swift */; };
		AF022BD7571EF5BCBB1EF3F1FD8EB7DD /* IQRootControllerConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0CC6DE7080F8F7B97850F31AC8ABE8F7 /* IQRootControllerConfiguration.swift */; };
		AF760C78F1C7E11BF7CB9E9B29903530 /* ConstraintInsets.swift in Sources */ = {isa = PBXBuildFile; fileRef = F98E2C9457B2AB09489823F7F9460054 /* ConstraintInsets.swift */; };
		B0875E3AB8718E7DFE5C53497C02A15E /* ConstraintLayoutSupport.swift in Sources */ = {isa = PBXBuildFile; fileRef = 71F751095AF6515098FA05AF42FBDEE3 /* ConstraintLayoutSupport.swift */; };
		B11B3A15203508AEF294D78D656AD8DA /* AAColumn.swift in Sources */ = {isa = PBXBuildFile; fileRef = B2A5499462201BA55C0A3C3907CA420E /* AAColumn.swift */; };
		B2B121881248EA5DF4A53AA03A6F40DE /* AALayoutAlgorithm.swift in Sources */ = {isa = PBXBuildFile; fileRef = 877EFDE461652310FC438FCCC3B5BD0F /* AALayoutAlgorithm.swift */; };
		B903049E7C1BED7918DAB208754107C7 /* ConstraintMakerFinalizable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4166CF971A8D112BE1944A55F52B6D39 /* ConstraintMakerFinalizable.swift */; };
		B9603E75D71200041A34A925D6F183BF /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 26AD26C46D7A9F0087E3AE4A72C9B2CF /* PrivacyInfo.xcprivacy */; };
		BA2FB695DEB0D179253EEB8DFCE3578B /* SnapKit-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 379916AD4322B47E998E121FFA8A3116 /* SnapKit-dummy.m */; };
		BBF8220B30095D0EB91D4933B4769D9E /* IQActiveConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3E2A36E16E1EC8168E1E206E74D27FD9 /* IQActiveConfiguration.swift */; };
		BDA5C7CC91E86448237CF40954FAC5AF /* ConstraintMakerRelatable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1E9D9651F6FF223B030D31C19E490762 /* ConstraintMakerRelatable.swift */; };
		BF1AE4D97E813B95C43EA4A298B973D1 /* LayoutConstraint.swift in Sources */ = {isa = PBXBuildFile; fileRef = 635FA142D44F8701C13DD235553B1E96 /* LayoutConstraint.swift */; };
		C07CB3E9A4D1BF00F841E4285629A2B2 /* ConstraintRelatableTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC183D8476C35E2EB6C99573DA05F3C8 /* ConstraintRelatableTarget.swift */; };
		C14F10B663FE2898EACAB90C202B3F50 /* ConstraintMakerEditable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5EEA492D88734A748646251DBAD2A522 /* ConstraintMakerEditable.swift */; };
		C3E307D86BC99500B675C38EDCBFB0AC /* IQInvocation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 545837F329ABB671B19DA6180A942F56 /* IQInvocation.swift */; };
		C40219960F480E093C1155A69D951C2C /* AAJSFiles.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 46F8A15F3E0475D30C5A6FAB8EBC3188 /* AAJSFiles.bundle */; };
		C46472E5D2A254C2EA873CEE3E9607D6 /* AATitle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5082FDFC8C0EC6822130911954A9F07B /* AATitle.swift */; };
		C6A4302ACE006C4E2CDD481287E2916B /* Typealiases.swift in Sources */ = {isa = PBXBuildFile; fileRef = 38BD960225DE4E14F6D93E3FB658125A /* Typealiases.swift */; };
		C6F45595676957ADBEC18EB3F23EAEC4 /* LayoutConstraintItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4851A7A1E59F6A375CD84C88A3C5C1E /* LayoutConstraintItem.swift */; };
		C892112213688A3A4523CDD9F323998B /* IQKeyboardReturnKeyHandler+TextFieldDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE6DB1F58274C0465205F06B4F5BF860 /* IQKeyboardReturnKeyHandler+TextFieldDelegate.swift */; };
		CCD99576883F2A882BE68EF947336D51 /* AAGradientColor+DefaultThemes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 887C1E50DAF0A76C9BFC404C38931E73 /* AAGradientColor+DefaultThemes.swift */; };
		CE593943A9E7CF83822CF60304BCAD43 /* ConstraintConstantTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = DD964421905CF776EAB81204975A40F0 /* ConstraintConstantTarget.swift */; };
		D0C9D7424D667076070CE75180ECCD6B /* IQToolbar.swift in Sources */ = {isa = PBXBuildFile; fileRef = C729F981E162E4971A977737D1D01640 /* IQToolbar.swift */; };
		D4218DA55B2BA45937589200CC0DF1FB /* ConstraintMakerExtendable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83BA9AB4AA05C5B19779260C54A342F3 /* ConstraintMakerExtendable.swift */; };
		D4E8DE674C6B890FE9D56D40BA77DD15 /* IQUIView+Hierarchy.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA1522F9FAC30EA2DA62E7FF993CA86F /* IQUIView+Hierarchy.swift */; };
		D8486770C951AD52186C9D105E060A60 /* AAGradientColor+LinearGradient.swift in Sources */ = {isa = PBXBuildFile; fileRef = B6E0CFA0636D5ED0DE045E6CA2A6E663 /* AAGradientColor+LinearGradient.swift */; };
		D86B9BE31D45BF36BE78559FBA4254E6 /* AAColor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CBD502083BD422E7FE665105DBC7EBC /* AAColor.swift */; };
		DBA4803F4765E1650B8C6841157F5D73 /* ConstraintPriorityTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1656B953ED727582B8BDB43BD21C7852 /* ConstraintPriorityTarget.swift */; };
		DC00471B9629D6BA4DECB8796D6532EC /* IQKeyboardListener.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74FFE795AD53DE10944DB5CC1B8C9029 /* IQKeyboardListener.swift */; };
		DDE98DAE6570A84BB1C07E14DA12C4D4 /* IQKeyboardManager+UIKeyboardNotification.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6F4B28CC14A7430B4DBB0CA33D35E71F /* IQKeyboardManager+UIKeyboardNotification.swift */; };
		E37671A03B4C17A1CF3766A6125833BB /* ConstraintDirectionalInsetTarget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3FF12E391298A594153C38F5C9BC4E4A /* ConstraintDirectionalInsetTarget.swift */; };
		E3D779DEE753C0B0D33BA8E73A980265 /* ConstraintLayoutGuide+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 391FF331ABD38614D9F4F46AA92E2534 /* ConstraintLayoutGuide+Extensions.swift */; };
		E3FD0840B77157C01FF5D52E72B31664 /* IQKeyboardManagerSwift-IQKeyboardManagerSwift in Resources */ = {isa = PBXBuildFile; fileRef = 8D8069D3964814114ACEC3084C010B59 /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */; };
		E5F8F6C107339F507114C3AB566ED585 /* IQUIView+IQKeyboardToolbar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 83D678BA791A5CB0D43B19C51A5614D4 /* IQUIView+IQKeyboardToolbar.swift */; };
		E7095898E9F45BD384B5BF177B642AFD /* IQUICollectionView+Additions.swift in Sources */ = {isa = PBXBuildFile; fileRef = F602DE22C562384F2CEF0AD121CB128E /* IQUICollectionView+Additions.swift */; };
		E7E7C94378820F81492BCF0CB798D46C /* IQKeyboardManager+Toolbar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 270F95B4A4D8C6141BA05B56BD19F604 /* IQKeyboardManager+Toolbar.swift */; };
		E862E5D575BED4D94CE1D4F24D841192 /* AAExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = AEEFA30FBF9F7C8096B33C038336F6DF /* AAExtension.swift */; };
		EB6131723350AB4DCB5AEDF242D458AC /* IQScrollViewConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = CDF719601060FC8488674EEB1E97ED6D /* IQScrollViewConfiguration.swift */; };
		ECC5C2ADC2682F9171FEA22AF10DCE53 /* ConstraintRelation.swift in Sources */ = {isa = PBXBuildFile; fileRef = FB1A4E994B86267EE9CA5ACDD11BC44D /* ConstraintRelation.swift */; };
		ED700CEFC48D83F8402C1577ECB94BC2 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = E714ED114B0ABDAB889EB62DA408F3C5 /* PrivacyInfo.xcprivacy */; };
		EE49210BAAF30051C28298E265D3D659 /* IQPlaceholderable.swift in Sources */ = {isa = PBXBuildFile; fileRef = EB1BBAA5278B97AF9E359F3B610EF1A7 /* IQPlaceholderable.swift */; };
		F52F7402A43AC9A8D04DEA11E1B802C6 /* AALabels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5A28CCEAE1A932C4DD4A20AFE65F2765 /* AALabels.swift */; };
		F633379AD81BA605AB1B01385C4B9248 /* IQKeyboardManagerConstants.swift in Sources */ = {isa = PBXBuildFile; fileRef = DA8980C212A3A872E0C381FB34D4357B /* IQKeyboardManagerConstants.swift */; };
		F6F33E8B268F3D41075374D95B8088DC /* UILayoutSupport+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74ECFFDF4D695B75B4B0263391B6B673 /* UILayoutSupport+Extensions.swift */; };
		F86C621FA07210C325437A3D8FA7CC68 /* IQKeyboardManager+Deprecated.swift in Sources */ = {isa = PBXBuildFile; fileRef = C90ED1176F88DF5B7C058E254BA7C69B /* IQKeyboardManager+Deprecated.swift */; };
		F98C041BD5C35C89C43DFBD4DF7EB712 /* AACredits.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1887E63997903AB44D3D64E36198E737 /* AACredits.swift */; };
		F9EBA65892D78A31C068D727D84BCB88 /* ConstraintConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC7C0E6517F0FAFB044A8E2CE21B3170 /* ConstraintConfig.swift */; };
		FA0B149FAA1A1C60DA9B92EC2569520E /* IQBarButtonItemConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5AE864409ACBC23678F40C74E3138E32 /* IQBarButtonItemConfiguration.swift */; };
		FE4B49773ED7A97B84316CF892D5D455 /* IQTextFieldViewInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = C17214AFBBAC4D98C9EA3001DF471DC5 /* IQTextFieldViewInfo.swift */; };
		FF8555B8EC8BC27E2A2C22D5075BE646 /* AALang.swift in Sources */ = {isa = PBXBuildFile; fileRef = F7D627836243B653A101A8465F04BDEC /* AALang.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		1AB9E2480ACC120E07ED306C5EB149F7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 19622742EBA51E823D6DAE3F8CDBFAD4;
			remoteInfo = SnapKit;
		};
		2D29126EC89554F7FE8ABDCC5B9DBFCE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B490E7485944099E16C9CBD79119D1D4;
			remoteInfo = IQKeyboardManagerSwift;
		};
		54C4CA56E598FDE9175DA0EED227E80F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8A8DB685241263AFDF5E6B20FE67B93A;
			remoteInfo = "SnapKit-SnapKit_Privacy";
		};
		665EEFD184B51C531244B39D9C836DFA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 982A68D37F5DCBC1FC1FDC0BB2F0EB8E;
			remoteInfo = "IQKeyboardManagerSwift-IQKeyboardManagerSwift";
		};
		6BF5813A60840FBE5026C0AB52432683 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 81AB6AA06BE8625CF139234A31B027FE;
			remoteInfo = AAInfographics;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		04A91AF5C89A203A66943E259030D56E /* AASeriesElement.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AASeriesElement.swift; path = AAInfographics/AAChartCreator/AASeriesElement.swift; sourceTree = "<group>"; };
		04F5F9E2F61334C2646D3042096986D4 /* AAInfographics-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AAInfographics-prefix.pch"; sourceTree = "<group>"; };
		063F91C01308FA71ED4E8B32BCB63DB4 /* AATooltip.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AATooltip.swift; path = AAInfographics/AAOptionsModel/AATooltip.swift; sourceTree = "<group>"; };
		069A551D0B6200D07EB296DDC229FC6E /* AAPlotBandsElement.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAPlotBandsElement.swift; path = AAInfographics/AAOptionsModel/AAPlotBandsElement.swift; sourceTree = "<group>"; };
		06C2F42F525AFD9989167107534D0413 /* ConstraintMakerRelatable+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "ConstraintMakerRelatable+Extensions.swift"; path = "Sources/ConstraintMakerRelatable+Extensions.swift"; sourceTree = "<group>"; };
		08DB70849B83F30C9141FEF7617A6B26 /* IQNSArray+Sort.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQNSArray+Sort.swift"; path = "IQKeyboardManagerSwift/UIKitExtensions/IQNSArray+Sort.swift"; sourceTree = "<group>"; };
		0916C40CF3EC3AB0C1049A7CC982EF11 /* AABoxplot.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AABoxplot.swift; path = AAInfographics/AAOptionsModel/AABoxplot.swift; sourceTree = "<group>"; };
		0B44D1146246358045F9241021CF368C /* IQKeyboardManager+Position.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+Position.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManager+Position.swift"; sourceTree = "<group>"; };
		0CC6DE7080F8F7B97850F31AC8ABE8F7 /* IQRootControllerConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQRootControllerConfiguration.swift; path = IQKeyboardManagerSwift/Configuration/IQRootControllerConfiguration.swift; sourceTree = "<group>"; };
		10AAA8D774CADF4B1B7851B0532531F8 /* Pods-ChargeSpot-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-ChargeSpot-dummy.m"; sourceTree = "<group>"; };
		141EB0D4CE4A086AAF239FD775213E8E /* Pods-ChargeSpot.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-ChargeSpot.modulemap"; sourceTree = "<group>"; };
		1656B953ED727582B8BDB43BD21C7852 /* ConstraintPriorityTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintPriorityTarget.swift; path = Sources/ConstraintPriorityTarget.swift; sourceTree = "<group>"; };
		1887E63997903AB44D3D64E36198E737 /* AACredits.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AACredits.swift; path = AAInfographics/AAOptionsModel/AACredits.swift; sourceTree = "<group>"; };
		1BB2E7DC6F46623C46A99E439E577A39 /* ConstraintDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDSL.swift; path = Sources/ConstraintDSL.swift; sourceTree = "<group>"; };
		1BD61D84A1CFA78D05835E84979EF817 /* AAGradientColor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAGradientColor.swift; path = AAInfographics/AATool/AAGradientColor.swift; sourceTree = "<group>"; };
		1BE29506F66C0B2EB180E5D42A992AA0 /* IQTextFieldViewInfoModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQTextFieldViewInfoModel.swift; path = IQKeyboardManagerSwift/ReturnKeyHandler/IQTextFieldViewInfoModel.swift; sourceTree = "<group>"; };
		1C3D0C9E374DD4F56223D64BBE9D67C3 /* AAStyle.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAStyle.swift; path = AAInfographics/AAOptionsModel/AAStyle.swift; sourceTree = "<group>"; };
		1D2C2C81E06765F6672A624A00006108 /* MainActor+AssumeIsolated.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "MainActor+AssumeIsolated.swift"; path = "IQKeyboardManagerSwift/UIKitExtensions/MainActor+AssumeIsolated.swift"; sourceTree = "<group>"; };
		1E9D9651F6FF223B030D31C19E490762 /* ConstraintMakerRelatable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerRelatable.swift; path = Sources/ConstraintMakerRelatable.swift; sourceTree = "<group>"; };
		1EBF0AF33E9F6E31641B7D1BB684376F /* SnapKit-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SnapKit-prefix.pch"; sourceTree = "<group>"; };
		208DA1D9940067EA989655C16E527AA9 /* IQKeyboardManagerSwift.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardManagerSwift.release.xcconfig; sourceTree = "<group>"; };
		20A2A456A86322ED11F6C9351576FC09 /* IQUITextFieldView+Additions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQUITextFieldView+Additions.swift"; path = "IQKeyboardManagerSwift/UIKitExtensions/IQUITextFieldView+Additions.swift"; sourceTree = "<group>"; };
		23201A67174F0451961F2B74F5414EC8 /* Debugging.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Debugging.swift; path = Sources/Debugging.swift; sourceTree = "<group>"; };
		24CF5A669371F7CD04FDEEC4CC2031D7 /* AAInfographics-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "AAInfographics-dummy.m"; sourceTree = "<group>"; };
		26AD26C46D7A9F0087E3AE4A72C9B2CF /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = Sources/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		270C1B5EA640DB553AE42470C7798AEC /* IQPreviousNextView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQPreviousNextView.swift; path = IQKeyboardManagerSwift/IQToolbar/IQPreviousNextView.swift; sourceTree = "<group>"; };
		270F95B4A4D8C6141BA05B56BD19F604 /* IQKeyboardManager+Toolbar.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+Toolbar.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManager+Toolbar.swift"; sourceTree = "<group>"; };
		2AB1B027EC52C740BC3F056B0F1F17D7 /* ConstraintMultiplierTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMultiplierTarget.swift; path = Sources/ConstraintMultiplierTarget.swift; sourceTree = "<group>"; };
		2DAE27E2B3E5B122C991DE3A66BAEC84 /* Pods-ChargeSpot.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-ChargeSpot.debug.xcconfig"; sourceTree = "<group>"; };
		32AF54A9F4796CD636751A693B4281C9 /* IQKeyboardManagerSwift-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardManagerSwift-umbrella.h"; sourceTree = "<group>"; };
		******************************** /* AABubbleLegend.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AABubbleLegend.swift; path = AAInfographics/AAOptionsModel/AABubbleLegend.swift; sourceTree = "<group>"; };
		379916AD4322B47E998E121FFA8A3116 /* SnapKit-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "SnapKit-dummy.m"; sourceTree = "<group>"; };
		38BD960225DE4E14F6D93E3FB658125A /* Typealiases.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Typealiases.swift; path = Sources/Typealiases.swift; sourceTree = "<group>"; };
		391FF331ABD38614D9F4F46AA92E2534 /* ConstraintLayoutGuide+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "ConstraintLayoutGuide+Extensions.swift"; path = "Sources/ConstraintLayoutGuide+Extensions.swift"; sourceTree = "<group>"; };
		3C515E0AC897C731289C54184A98A795 /* AAChart.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAChart.swift; path = AAInfographics/AAOptionsModel/AAChart.swift; sourceTree = "<group>"; };
		3E2A36E16E1EC8168E1E206E74D27FD9 /* IQActiveConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQActiveConfiguration.swift; path = IQKeyboardManagerSwift/Configuration/IQActiveConfiguration.swift; sourceTree = "<group>"; };
		3FF12E391298A594153C38F5C9BC4E4A /* ConstraintDirectionalInsetTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDirectionalInsetTarget.swift; path = Sources/ConstraintDirectionalInsetTarget.swift; sourceTree = "<group>"; };
		4166CF971A8D112BE1944A55F52B6D39 /* ConstraintMakerFinalizable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerFinalizable.swift; path = Sources/ConstraintMakerFinalizable.swift; sourceTree = "<group>"; };
		419CB0F7F66FEAF40DD0EADF5509659A /* AAPlotLinesElement.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAPlotLinesElement.swift; path = AAInfographics/AAOptionsModel/AAPlotLinesElement.swift; sourceTree = "<group>"; };
		449692FFD3124EC6C5FD624C59F126ED /* IQUIViewController+Additions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQUIViewController+Additions.swift"; path = "IQKeyboardManagerSwift/UIKitExtensions/IQUIViewController+Additions.swift"; sourceTree = "<group>"; };
		459A45FEBCEBE298BEE465D480035D7A /* ConstraintInsetTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintInsetTarget.swift; path = Sources/ConstraintInsetTarget.swift; sourceTree = "<group>"; };
		46F8A15F3E0475D30C5A6FAB8EBC3188 /* AAJSFiles.bundle */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = "wrapper.plug-in"; name = AAJSFiles.bundle; path = AAInfographics/AAJSFiles.bundle; sourceTree = "<group>"; };
		47E7C221D88EA41368E545A8174DD365 /* AAInfographics.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AAInfographics.debug.xcconfig; sourceTree = "<group>"; };
		483D6FF787DF666ABAF5013EFAD42A13 /* AALegend.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AALegend.swift; path = AAInfographics/AAOptionsModel/AALegend.swift; sourceTree = "<group>"; };
		4A9D8A008A2704939DA1E21BC5DEC047 /* IQKeyboardManager+ToolbarActions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+ToolbarActions.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManager+ToolbarActions.swift"; sourceTree = "<group>"; };
		4AB6104F539C1671A460348EE54AB0A4 /* AAPackedbubble.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAPackedbubble.swift; path = AAInfographics/AAOptionsModel/AAPackedbubble.swift; sourceTree = "<group>"; };
		4BCF6A1012C98D75A6011FEC7D445536 /* Pods-ChargeSpot-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-ChargeSpot-umbrella.h"; sourceTree = "<group>"; };
		4C89B730732A92E19E9E9B580E5A5B74 /* IQTextView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQTextView.swift; path = IQKeyboardManagerSwift/IQTextView/IQTextView.swift; sourceTree = "<group>"; };
		4CBD502083BD422E7FE665105DBC7EBC /* AAColor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAColor.swift; path = AAInfographics/AATool/AAColor.swift; sourceTree = "<group>"; };
		4FD5C6E1EA88852FEF8628F34A4C0BCA /* IQTitleBarButtonItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQTitleBarButtonItem.swift; path = IQKeyboardManagerSwift/IQToolbar/IQTitleBarButtonItem.swift; sourceTree = "<group>"; };
		4FFD26A6443E6C39B3C83BA581406A1D /* AAPlotOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAPlotOptions.swift; path = AAInfographics/AAOptionsModel/AAPlotOptions.swift; sourceTree = "<group>"; };
		5061C5644507E3A08CCA27AE872C6CF9 /* AAXAxis.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAXAxis.swift; path = AAInfographics/AAOptionsModel/AAXAxis.swift; sourceTree = "<group>"; };
		5082FDFC8C0EC6822130911954A9F07B /* AATitle.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AATitle.swift; path = AAInfographics/AAOptionsModel/AATitle.swift; sourceTree = "<group>"; };
		52950D23954B62BA8DEDC65AA88B2908 /* IQKeyboardManager+Internal.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+Internal.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManager+Internal.swift"; sourceTree = "<group>"; };
		545837F329ABB671B19DA6180A942F56 /* IQInvocation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQInvocation.swift; path = IQKeyboardManagerSwift/IQToolbar/IQInvocation.swift; sourceTree = "<group>"; };
		5471F27C9FC5A9F05558B10F1837DFA9 /* ConstraintAttributes.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintAttributes.swift; path = Sources/ConstraintAttributes.swift; sourceTree = "<group>"; };
		550887E8DA7F780888F21EE1C8CAE165 /* ConstraintMaker.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMaker.swift; path = Sources/ConstraintMaker.swift; sourceTree = "<group>"; };
		5A28CCEAE1A932C4DD4A20AFE65F2765 /* AALabels.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AALabels.swift; path = AAInfographics/AAOptionsModel/AALabels.swift; sourceTree = "<group>"; };
		5AE864409ACBC23678F40C74E3138E32 /* IQBarButtonItemConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQBarButtonItemConfiguration.swift; path = IQKeyboardManagerSwift/Configuration/IQBarButtonItemConfiguration.swift; sourceTree = "<group>"; };
		5AEB23950B83A8D1002278B4EC108F39 /* ConstraintMakerPrioritizable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerPrioritizable.swift; path = Sources/ConstraintMakerPrioritizable.swift; sourceTree = "<group>"; };
		5CD1FC29720287740820EF2F7613FDCE /* ConstraintView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintView.swift; path = Sources/ConstraintView.swift; sourceTree = "<group>"; };
		5EEA492D88734A748646251DBAD2A522 /* ConstraintMakerEditable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerEditable.swift; path = Sources/ConstraintMakerEditable.swift; sourceTree = "<group>"; };
		5F2E4452C65B1A0C240DD8CF7592F81A /* Pods-ChargeSpot */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = "Pods-ChargeSpot"; path = Pods_ChargeSpot.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5F3514E3F21055E195A5942DA3F38AD5 /* AASeries.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AASeries.swift; path = AAInfographics/AAOptionsModel/AASeries.swift; sourceTree = "<group>"; };
		605546073AF43F60F673269621B44E0D /* ConstraintLayoutGuideDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutGuideDSL.swift; path = Sources/ConstraintLayoutGuideDSL.swift; sourceTree = "<group>"; };
		635FA142D44F8701C13DD235553B1E96 /* LayoutConstraint.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LayoutConstraint.swift; path = Sources/LayoutConstraint.swift; sourceTree = "<group>"; };
		63AF321CEA03A02B1345A22B88B465CE /* ConstraintLayoutGuide.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutGuide.swift; path = Sources/ConstraintLayoutGuide.swift; sourceTree = "<group>"; };
		63F2E7A289EE6CAE91B493D8878EE1B6 /* AAInfographics.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = AAInfographics.release.xcconfig; sourceTree = "<group>"; };
		69301F4E598400CA4CB88505EAF858B1 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
		69A152D7513271B18C120B40BAD3701A /* Pods-ChargeSpot-frameworks.sh */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.script.sh; path = "Pods-ChargeSpot-frameworks.sh"; sourceTree = "<group>"; };
		6C48CBC3A65804D80F45F4F857055A7F /* ConstraintPriority.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintPriority.swift; path = Sources/ConstraintPriority.swift; sourceTree = "<group>"; };
		6EDB1CB2DE510F33C8DA683776CE5D2B /* AAChartView.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAChartView.swift; path = AAInfographics/AAChartCreator/AAChartView.swift; sourceTree = "<group>"; };
		6EF0BE6555E9CAE20A713625296145A7 /* AAScatter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAScatter.swift; path = AAInfographics/AAOptionsModel/AAScatter.swift; sourceTree = "<group>"; };
		6F4B28CC14A7430B4DBB0CA33D35E71F /* IQKeyboardManager+UIKeyboardNotification.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+UIKeyboardNotification.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManager+UIKeyboardNotification.swift"; sourceTree = "<group>"; };
		71BB6C68136D7C7D29FDCAE97FA070DD /* AAStates.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAStates.swift; path = AAInfographics/AAOptionsModel/AAStates.swift; sourceTree = "<group>"; };
		71F751095AF6515098FA05AF42FBDEE3 /* ConstraintLayoutSupport.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutSupport.swift; path = Sources/ConstraintLayoutSupport.swift; sourceTree = "<group>"; };
		7245C51D5B2203AE0EFC04500217F758 /* IQKeyboardInfo.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardInfo.swift; path = IQKeyboardManagerSwift/LIsteners/Info/IQKeyboardInfo.swift; sourceTree = "<group>"; };
		735F64F42825B664FB6A870FB8A0D27F /* IQKeyboardManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardManager.swift; path = IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManager.swift; sourceTree = "<group>"; };
		74ECFFDF4D695B75B4B0263391B6B673 /* UILayoutSupport+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UILayoutSupport+Extensions.swift"; path = "Sources/UILayoutSupport+Extensions.swift"; sourceTree = "<group>"; };
		74FFE795AD53DE10944DB5CC1B8C9029 /* IQKeyboardListener.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardListener.swift; path = IQKeyboardManagerSwift/LIsteners/IQKeyboardListener.swift; sourceTree = "<group>"; };
		75C6F3B3F08C44B224062AFCA5FCB1FC /* ConstraintOffsetTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintOffsetTarget.swift; path = Sources/ConstraintOffsetTarget.swift; sourceTree = "<group>"; };
		772D9587D0849D0DFF1C1EC45320D6AF /* SnapKit.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SnapKit.release.xcconfig; sourceTree = "<group>"; };
		77BB3CE04DFFC883C9616BAABFAAD824 /* AAYAxis.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAYAxis.swift; path = AAInfographics/AAOptionsModel/AAYAxis.swift; sourceTree = "<group>"; };
		79B80460DD5D31C4F123D9DCDC0CDB28 /* Pods-ChargeSpot.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-ChargeSpot.release.xcconfig"; sourceTree = "<group>"; };
		7D9915F67AD1CD0832E475399DFDD1DE /* AAAnimation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAAnimation.swift; path = AAInfographics/AAOptionsModel/AAAnimation.swift; sourceTree = "<group>"; };
		8129CBFC4A84C1656433BBCD0034DAEB /* SnapKit.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = SnapKit.modulemap; sourceTree = "<group>"; };
		81F03334F91C8ECA31583E83709E358D /* Pods-ChargeSpot-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-ChargeSpot-acknowledgements.plist"; sourceTree = "<group>"; };
		83BA9AB4AA05C5B19779260C54A342F3 /* ConstraintMakerExtendable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintMakerExtendable.swift; path = Sources/ConstraintMakerExtendable.swift; sourceTree = "<group>"; };
		83D678BA791A5CB0D43B19C51A5614D4 /* IQUIView+IQKeyboardToolbar.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQUIView+IQKeyboardToolbar.swift"; path = "IQKeyboardManagerSwift/IQToolbar/IQUIView+IQKeyboardToolbar.swift"; sourceTree = "<group>"; };
		877EFDE461652310FC438FCCC3B5BD0F /* AALayoutAlgorithm.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AALayoutAlgorithm.swift; path = AAInfographics/AAOptionsModel/AALayoutAlgorithm.swift; sourceTree = "<group>"; };
		87CD60B71058F794E6B3653B0E28DC18 /* IQKeyboardManagerSwift.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = IQKeyboardManagerSwift.modulemap; sourceTree = "<group>"; };
		887C1E50DAF0A76C9BFC404C38931E73 /* AAGradientColor+DefaultThemes.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AAGradientColor+DefaultThemes.swift"; path = "AAInfographics/AATool/AAGradientColor+DefaultThemes.swift"; sourceTree = "<group>"; };
		8989A59ADC6A439686C9BE31BCC827A8 /* ConstraintViewDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintViewDSL.swift; path = Sources/ConstraintViewDSL.swift; sourceTree = "<group>"; };
		8D8069D3964814114ACEC3084C010B59 /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "IQKeyboardManagerSwift-IQKeyboardManagerSwift"; path = IQKeyboardManagerSwift.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		9106F9AB42732BB5BF87F44EE52534AA /* AASubtitle.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AASubtitle.swift; path = AAInfographics/AAOptionsModel/AASubtitle.swift; sourceTree = "<group>"; };
		91CA0E574C6CEC3FA180547E105DFC92 /* IQKeyboardManagerSwift-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "IQKeyboardManagerSwift-Info.plist"; sourceTree = "<group>"; };
		931B256AE49B31B2919957A56690B478 /* UIImage+NextPrevious.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "UIImage+NextPrevious.swift"; path = "IQKeyboardManagerSwift/UIKitExtensions/UIImage+NextPrevious.swift"; sourceTree = "<group>"; };
		979486118B3E90C08386079D57962701 /* SnapKit */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = SnapKit; path = SnapKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		994CAF88FFE5AF0B5EBA4224FD47E4A1 /* AAAxis.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAAxis.swift; path = AAInfographics/AAOptionsModel/AAAxis.swift; sourceTree = "<group>"; };
		9B39734B699CD68B73BAE285F10E54CA /* IQToolbarPlaceholderConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQToolbarPlaceholderConfiguration.swift; path = IQKeyboardManagerSwift/Configuration/IQToolbarPlaceholderConfiguration.swift; sourceTree = "<group>"; };
		9CE1B6592A29ADBBC5FB53143C4D4955 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreGraphics.framework; sourceTree = DEVELOPER_DIR; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		A06E7979FF01D8CE948AAEA07223CC27 /* Constraint.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Constraint.swift; path = Sources/Constraint.swift; sourceTree = "<group>"; };
		A0CFC24F45E44BA1194FA744417F3A8D /* IQKeyboardManager+Debug.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+Debug.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManager+Debug.swift"; sourceTree = "<group>"; };
		A1A8B150F5ED29E5F4CE59330F4AB35A /* ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist"; sourceTree = "<group>"; };
		A2302A35710DB980F5D0CD9A7D204ABF /* IQKeyboardManagerSwift-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "IQKeyboardManagerSwift-dummy.m"; sourceTree = "<group>"; };
		A4174F518D19D066685A458F326B8239 /* AAInfographics.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = AAInfographics.modulemap; sourceTree = "<group>"; };
		A69D4E881ED3E3E0FB12176B8A7FF999 /* IQUIScrollView+Additions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQUIScrollView+Additions.swift"; path = "IQKeyboardManagerSwift/UIKitExtensions/IQUIScrollView+Additions.swift"; sourceTree = "<group>"; };
		A8E950A16D00F649C54FFB30F81D7842 /* IQKeyboardManagerSwift */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = IQKeyboardManagerSwift; path = IQKeyboardManagerSwift.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A912741BDA04F14F5D091E6DD6B95F90 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/QuartzCore.framework; sourceTree = DEVELOPER_DIR; };
		AA1522F9FAC30EA2DA62E7FF993CA86F /* IQUIView+Hierarchy.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQUIView+Hierarchy.swift"; path = "IQKeyboardManagerSwift/UIKitExtensions/IQUIView+Hierarchy.swift"; sourceTree = "<group>"; };
		ABFFE1C03AE79E8EB45757FD3CDA33DD /* SnapKit.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = SnapKit.debug.xcconfig; sourceTree = "<group>"; };
		ACC2B8C0FDB7AF11990DCF1E194190C7 /* AAChartViewPluginProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAChartViewPluginProvider.swift; path = AAInfographics/AAChartCreator/AAChartViewPluginProvider.swift; sourceTree = "<group>"; };
		AEEFA30FBF9F7C8096B33C038336F6DF /* AAExtension.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAExtension.swift; path = AAInfographics/AATool/AAExtension.swift; sourceTree = "<group>"; };
		B18E9E42F6DB3D1A4662C0E6B0948B20 /* IQTextFieldViewListener.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQTextFieldViewListener.swift; path = IQKeyboardManagerSwift/LIsteners/IQTextFieldViewListener.swift; sourceTree = "<group>"; };
		B2A5499462201BA55C0A3C3907CA420E /* AAColumn.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAColumn.swift; path = AAInfographics/AAOptionsModel/AAColumn.swift; sourceTree = "<group>"; };
		B6E0CFA0636D5ED0DE045E6CA2A6E663 /* AAGradientColor+LinearGradient.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AAGradientColor+LinearGradient.swift"; path = "AAInfographics/AATool/AAGradientColor+LinearGradient.swift"; sourceTree = "<group>"; };
		B7408994E8B02F05DD4E5D2D1EA25EC2 /* AAGradientColor+RadialGradient.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AAGradientColor+RadialGradient.swift"; path = "AAInfographics/AATool/AAGradientColor+RadialGradient.swift"; sourceTree = "<group>"; };
		B8261D73E5486EBCF552FC35653D39F4 /* AAScrollablePlotArea.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAScrollablePlotArea.swift; path = AAInfographics/AAOptionsModel/AAScrollablePlotArea.swift; sourceTree = "<group>"; };
		B979BCAFEA5C9EC623AB08B15FFA21FD /* IQToolbarConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQToolbarConfiguration.swift; path = IQKeyboardManagerSwift/Configuration/IQToolbarConfiguration.swift; sourceTree = "<group>"; };
		B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; name = "SnapKit-SnapKit_Privacy"; path = SnapKit_Privacy.bundle; sourceTree = BUILT_PRODUCTS_DIR; };
		BE6DB1F58274C0465205F06B4F5BF860 /* IQKeyboardReturnKeyHandler+TextFieldDelegate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardReturnKeyHandler+TextFieldDelegate.swift"; path = "IQKeyboardManagerSwift/ReturnKeyHandler/IQKeyboardReturnKeyHandler+TextFieldDelegate.swift"; sourceTree = "<group>"; };
		C17214AFBBAC4D98C9EA3001DF471DC5 /* IQTextFieldViewInfo.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQTextFieldViewInfo.swift; path = IQKeyboardManagerSwift/LIsteners/Info/IQTextFieldViewInfo.swift; sourceTree = "<group>"; };
		C17FAE3B8C86B4D47498B99BA6B1824B /* Pods-ChargeSpot-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-ChargeSpot-acknowledgements.markdown"; sourceTree = "<group>"; };
		C287C539748BDC4A6E64E459102794E5 /* ConstraintDirectionalInsets.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDirectionalInsets.swift; path = Sources/ConstraintDirectionalInsets.swift; sourceTree = "<group>"; };
		C3B70A3507AB387C4C19593B30AB18AC /* ResourceBundle-IQKeyboardManagerSwift-IQKeyboardManagerSwift-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "ResourceBundle-IQKeyboardManagerSwift-IQKeyboardManagerSwift-Info.plist"; sourceTree = "<group>"; };
		C4851A7A1E59F6A375CD84C88A3C5C1E /* LayoutConstraintItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LayoutConstraintItem.swift; path = Sources/LayoutConstraintItem.swift; sourceTree = "<group>"; };
		C4E28C64704DBDA820F361F088CFAFE6 /* AAOptions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAOptions.swift; path = AAInfographics/AAChartCreator/AAOptions.swift; sourceTree = "<group>"; };
		C5C0857160FC1F5CC0EDD7CE22DCFE87 /* AAPie.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAPie.swift; path = AAInfographics/AAOptionsModel/AAPie.swift; sourceTree = "<group>"; };
		C665C3EEBF85BE7F61EF12436F008782 /* IQUIView+IQKeyboardToolbarDeprecated.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQUIView+IQKeyboardToolbarDeprecated.swift"; path = "IQKeyboardManagerSwift/IQToolbar/IQUIView+IQKeyboardToolbarDeprecated.swift"; sourceTree = "<group>"; };
		C6E2D909636C86AC957D5CE04F4128D3 /* AAChartView+API.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "AAChartView+API.swift"; path = "AAInfographics/AAChartCreator/AAChartView+API.swift"; sourceTree = "<group>"; };
		C729F981E162E4971A977737D1D01640 /* IQToolbar.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQToolbar.swift; path = IQKeyboardManagerSwift/IQToolbar/IQToolbar.swift; sourceTree = "<group>"; };
		C7773C7D7B5BF5AC5B5FEBE20BAB79AA /* AASerializable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AASerializable.swift; path = AAInfographics/AAChartCreator/AASerializable.swift; sourceTree = "<group>"; };
		C90ED1176F88DF5B7C058E254BA7C69B /* IQKeyboardManager+Deprecated.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+Deprecated.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManager+Deprecated.swift"; sourceTree = "<group>"; };
		CC183D8476C35E2EB6C99573DA05F3C8 /* ConstraintRelatableTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintRelatableTarget.swift; path = Sources/ConstraintRelatableTarget.swift; sourceTree = "<group>"; };
		CC7C0E6517F0FAFB044A8E2CE21B3170 /* ConstraintConfig.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintConfig.swift; path = Sources/ConstraintConfig.swift; sourceTree = "<group>"; };
		CDF719601060FC8488674EEB1E97ED6D /* IQScrollViewConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQScrollViewConfiguration.swift; path = IQKeyboardManagerSwift/Configuration/IQScrollViewConfiguration.swift; sourceTree = "<group>"; };
		CF2F76829B4085620945DEA25C375C39 /* AAInfographics-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "AAInfographics-Info.plist"; sourceTree = "<group>"; };
		D03D93D9AD4368659CCBFE34345185F8 /* AAInfographics-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "AAInfographics-umbrella.h"; sourceTree = "<group>"; };
		D65EF91816145FD72DA8295640A52596 /* AALabel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AALabel.swift; path = AAInfographics/AAOptionsModel/AALabel.swift; sourceTree = "<group>"; };
		D660894493A3DCFEBB6589B9D5E4D266 /* Pods-ChargeSpot-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-ChargeSpot-Info.plist"; sourceTree = "<group>"; };
		D6CCB9D258E15D810E80DB9950E2B5AC /* AAInfographics */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; name = AAInfographics; path = AAInfographics.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DA8980C212A3A872E0C381FB34D4357B /* IQKeyboardManagerConstants.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardManagerConstants.swift; path = IQKeyboardManagerSwift/Constants/IQKeyboardManagerConstants.swift; sourceTree = "<group>"; };
		DD964421905CF776EAB81204975A40F0 /* ConstraintConstantTarget.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintConstantTarget.swift; path = Sources/ConstraintConstantTarget.swift; sourceTree = "<group>"; };
		DD988279CBF80937BF54DECF9BD39BEF /* IQKeyboardReturnKeyHandler+TextViewDelegate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardReturnKeyHandler+TextViewDelegate.swift"; path = "IQKeyboardManagerSwift/ReturnKeyHandler/IQKeyboardReturnKeyHandler+TextViewDelegate.swift"; sourceTree = "<group>"; };
		DF330D9FBA848850DA54AB516A4C6A10 /* IQUITableView+Additions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQUITableView+Additions.swift"; path = "IQKeyboardManagerSwift/UIKitExtensions/IQUITableView+Additions.swift"; sourceTree = "<group>"; };
		E00481C8F4DAA1927620296FC1202DF8 /* AAPane.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAPane.swift; path = AAInfographics/AAOptionsModel/AAPane.swift; sourceTree = "<group>"; };
		E03F894CAA5A9D681C103609D6AB734B /* ConstraintView+Extensions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "ConstraintView+Extensions.swift"; path = "Sources/ConstraintView+Extensions.swift"; sourceTree = "<group>"; };
		E173C42A0A833109802BF258B4984666 /* IQKeyboardManagerCompatible.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardManagerCompatible.swift; path = IQKeyboardManagerSwift/IQKeyboardManagerCompatible/IQKeyboardManagerCompatible.swift; sourceTree = "<group>"; };
		E176AFB682B78E2894FDAB3C089D03F9 /* IQBarButtonItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQBarButtonItem.swift; path = IQKeyboardManagerSwift/IQToolbar/IQBarButtonItem.swift; sourceTree = "<group>"; };
		E1F16131E80C86699226A8045B09BC93 /* AACrosshair.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AACrosshair.swift; path = AAInfographics/AAOptionsModel/AACrosshair.swift; sourceTree = "<group>"; };
		E213D89E47AE3679ADB1CB0AAEF3A8E1 /* ConstraintDescription.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintDescription.swift; path = Sources/ConstraintDescription.swift; sourceTree = "<group>"; };
		E25CF85AA5546863B9667D2439B13F5F /* SnapKit-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "SnapKit-Info.plist"; sourceTree = "<group>"; };
		E34BE6459764A97A2F8DBD0426DF42A2 /* IQKeyboardManagerSwift-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "IQKeyboardManagerSwift-prefix.pch"; sourceTree = "<group>"; };
		E53B5E444D65BD6CFA1F562F80DE610B /* AAMarker.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAMarker.swift; path = AAInfographics/AAOptionsModel/AAMarker.swift; sourceTree = "<group>"; };
		E714ED114B0ABDAB889EB62DA408F3C5 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = IQKeyboardManagerSwift/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		E94478C0EE33FA6880675BA62034B30B /* AAChartModel.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAChartModel.swift; path = AAInfographics/AAChartCreator/AAChartModel.swift; sourceTree = "<group>"; };
		EB1BBAA5278B97AF9E359F3B610EF1A7 /* IQPlaceholderable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQPlaceholderable.swift; path = IQKeyboardManagerSwift/IQTextView/IQPlaceholderable.swift; sourceTree = "<group>"; };
		ED3234428715ED881C945B13B58F9250 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework; sourceTree = DEVELOPER_DIR; };
		F00CA9C87938AE674D9C17133FD3E262 /* ConstraintLayoutSupportDSL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintLayoutSupportDSL.swift; path = Sources/ConstraintLayoutSupportDSL.swift; sourceTree = "<group>"; };
		F0AB94E52BD76051BE55DFDB83A9F8C1 /* ConstraintItem.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintItem.swift; path = Sources/ConstraintItem.swift; sourceTree = "<group>"; };
		F5A02C54472B6F3EC32A0160D8A57200 /* AADataLabels.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AADataLabels.swift; path = AAInfographics/AAOptionsModel/AADataLabels.swift; sourceTree = "<group>"; };
		F5CB8ACCF4F8D71BCE60B87052B0B347 /* ProjectBundlePathLoader.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ProjectBundlePathLoader.swift; path = AAInfographics/ProjectBundlePathLoader.swift; sourceTree = "<group>"; };
		F602DE22C562384F2CEF0AD121CB128E /* IQUICollectionView+Additions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQUICollectionView+Additions.swift"; path = "IQKeyboardManagerSwift/UIKitExtensions/IQUICollectionView+Additions.swift"; sourceTree = "<group>"; };
		F65EC09FBD198C1555CDEDF55C597C17 /* IQKeyboardManagerSwift.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = IQKeyboardManagerSwift.debug.xcconfig; sourceTree = "<group>"; };
		F6F75BA63EFB3F9CCA59C17FC7B80582 /* IQKeyboardReturnKeyHandler.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardReturnKeyHandler.swift; path = IQKeyboardManagerSwift/ReturnKeyHandler/IQKeyboardReturnKeyHandler.swift; sourceTree = "<group>"; };
		F755C70D65DD35629230631996D38E4F /* IQKeyboardConfiguration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IQKeyboardConfiguration.swift; path = IQKeyboardManagerSwift/Configuration/IQKeyboardConfiguration.swift; sourceTree = "<group>"; };
		F7D627836243B653A101A8465F04BDEC /* AALang.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AALang.swift; path = AAInfographics/AAOptionsModel/AALang.swift; sourceTree = "<group>"; };
		F98E2C9457B2AB09489823F7F9460054 /* ConstraintInsets.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintInsets.swift; path = Sources/ConstraintInsets.swift; sourceTree = "<group>"; };
		FA7712C6E6B40A90C46A2CC450F20E26 /* SnapKit-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "SnapKit-umbrella.h"; sourceTree = "<group>"; };
		FB1A4E994B86267EE9CA5ACDD11BC44D /* ConstraintRelation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ConstraintRelation.swift; path = Sources/ConstraintRelation.swift; sourceTree = "<group>"; };
		FC90D5D73117BFE6B72AC241483DD5BB /* AAButtonTheme.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AAButtonTheme.swift; path = AAInfographics/AAOptionsModel/AAButtonTheme.swift; sourceTree = "<group>"; };
		FCFD46E8DA59B6DB18C6D8F420E9E5E7 /* IQKeyboardManager+UITextFieldViewNotification.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "IQKeyboardManager+UITextFieldViewNotification.swift"; path = "IQKeyboardManagerSwift/IQKeyboardManager/IQKeyboardManager+UITextFieldViewNotification.swift"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1750A41D019B093F9660121890B53E83 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0301973EE3FDE2CFB75C3D6445F25992 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		33428AC36668E3ED52DB70316F843FB8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				205EB01AED14BB574DD54EAFE26E4786 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		79E123CE7C2BE00816E19CE19C99028D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9471927F8AAA87EA82DBEC2880E23A1D /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		880D9639347E9BEC56915425CDF9EF87 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A76B7572AD1CD0125BA8227332330DC9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				663F1294E62250BD0DF7888F2173B0D6 /* CoreGraphics.framework in Frameworks */,
				851B0A1B6750B5464D9300F8DE62CAF2 /* Foundation.framework in Frameworks */,
				5CA32A2520B7868CF67B02C1C830F5A8 /* QuartzCore.framework in Frameworks */,
				743A1D7C259156C99A14E4B1FD01A52C /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E3E5D4F7883FAA199D7DFED0B2313BEF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		143C29E3BE492836E12A84EF2EC76B27 /* iOS */ = {
			isa = PBXGroup;
			children = (
				9CE1B6592A29ADBBC5FB53143C4D4955 /* CoreGraphics.framework */,
				ED3234428715ED881C945B13B58F9250 /* Foundation.framework */,
				A912741BDA04F14F5D091E6DD6B95F90 /* QuartzCore.framework */,
				69301F4E598400CA4CB88505EAF858B1 /* UIKit.framework */,
			);
			name = iOS;
			sourceTree = "<group>";
		};
		2F68E75A0CB33B5A8D996D0B52A36ACB /* Pods-ChargeSpot */ = {
			isa = PBXGroup;
			children = (
				141EB0D4CE4A086AAF239FD775213E8E /* Pods-ChargeSpot.modulemap */,
				C17FAE3B8C86B4D47498B99BA6B1824B /* Pods-ChargeSpot-acknowledgements.markdown */,
				81F03334F91C8ECA31583E83709E358D /* Pods-ChargeSpot-acknowledgements.plist */,
				10AAA8D774CADF4B1B7851B0532531F8 /* Pods-ChargeSpot-dummy.m */,
				69A152D7513271B18C120B40BAD3701A /* Pods-ChargeSpot-frameworks.sh */,
				D660894493A3DCFEBB6589B9D5E4D266 /* Pods-ChargeSpot-Info.plist */,
				4BCF6A1012C98D75A6011FEC7D445536 /* Pods-ChargeSpot-umbrella.h */,
				2DAE27E2B3E5B122C991DE3A66BAEC84 /* Pods-ChargeSpot.debug.xcconfig */,
				79B80460DD5D31C4F123D9DCDC0CDB28 /* Pods-ChargeSpot.release.xcconfig */,
			);
			name = "Pods-ChargeSpot";
			path = "Target Support Files/Pods-ChargeSpot";
			sourceTree = "<group>";
		};
		3E356EBC242C1B70CC3B6A3540E33378 /* Resources */ = {
			isa = PBXGroup;
			children = (
				E714ED114B0ABDAB889EB62DA408F3C5 /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		5D3420AA52A5348BE32D92C2DCBA8764 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				A4174F518D19D066685A458F326B8239 /* AAInfographics.modulemap */,
				24CF5A669371F7CD04FDEEC4CC2031D7 /* AAInfographics-dummy.m */,
				CF2F76829B4085620945DEA25C375C39 /* AAInfographics-Info.plist */,
				04F5F9E2F61334C2646D3042096986D4 /* AAInfographics-prefix.pch */,
				D03D93D9AD4368659CCBFE34345185F8 /* AAInfographics-umbrella.h */,
				47E7C221D88EA41368E545A8174DD365 /* AAInfographics.debug.xcconfig */,
				63F2E7A289EE6CAE91B493D8878EE1B6 /* AAInfographics.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/AAInfographics";
			sourceTree = "<group>";
		};
		6939E259D36A1ABA9A4127D56F88635F /* IQKeyboardManagerSwift */ = {
			isa = PBXGroup;
			children = (
				3E2A36E16E1EC8168E1E206E74D27FD9 /* IQActiveConfiguration.swift */,
				E176AFB682B78E2894FDAB3C089D03F9 /* IQBarButtonItem.swift */,
				5AE864409ACBC23678F40C74E3138E32 /* IQBarButtonItemConfiguration.swift */,
				545837F329ABB671B19DA6180A942F56 /* IQInvocation.swift */,
				F755C70D65DD35629230631996D38E4F /* IQKeyboardConfiguration.swift */,
				7245C51D5B2203AE0EFC04500217F758 /* IQKeyboardInfo.swift */,
				74FFE795AD53DE10944DB5CC1B8C9029 /* IQKeyboardListener.swift */,
				735F64F42825B664FB6A870FB8A0D27F /* IQKeyboardManager.swift */,
				A0CFC24F45E44BA1194FA744417F3A8D /* IQKeyboardManager+Debug.swift */,
				C90ED1176F88DF5B7C058E254BA7C69B /* IQKeyboardManager+Deprecated.swift */,
				52950D23954B62BA8DEDC65AA88B2908 /* IQKeyboardManager+Internal.swift */,
				0B44D1146246358045F9241021CF368C /* IQKeyboardManager+Position.swift */,
				270F95B4A4D8C6141BA05B56BD19F604 /* IQKeyboardManager+Toolbar.swift */,
				4A9D8A008A2704939DA1E21BC5DEC047 /* IQKeyboardManager+ToolbarActions.swift */,
				6F4B28CC14A7430B4DBB0CA33D35E71F /* IQKeyboardManager+UIKeyboardNotification.swift */,
				FCFD46E8DA59B6DB18C6D8F420E9E5E7 /* IQKeyboardManager+UITextFieldViewNotification.swift */,
				E173C42A0A833109802BF258B4984666 /* IQKeyboardManagerCompatible.swift */,
				DA8980C212A3A872E0C381FB34D4357B /* IQKeyboardManagerConstants.swift */,
				F6F75BA63EFB3F9CCA59C17FC7B80582 /* IQKeyboardReturnKeyHandler.swift */,
				BE6DB1F58274C0465205F06B4F5BF860 /* IQKeyboardReturnKeyHandler+TextFieldDelegate.swift */,
				DD988279CBF80937BF54DECF9BD39BEF /* IQKeyboardReturnKeyHandler+TextViewDelegate.swift */,
				08DB70849B83F30C9141FEF7617A6B26 /* IQNSArray+Sort.swift */,
				EB1BBAA5278B97AF9E359F3B610EF1A7 /* IQPlaceholderable.swift */,
				270C1B5EA640DB553AE42470C7798AEC /* IQPreviousNextView.swift */,
				0CC6DE7080F8F7B97850F31AC8ABE8F7 /* IQRootControllerConfiguration.swift */,
				CDF719601060FC8488674EEB1E97ED6D /* IQScrollViewConfiguration.swift */,
				C17214AFBBAC4D98C9EA3001DF471DC5 /* IQTextFieldViewInfo.swift */,
				1BE29506F66C0B2EB180E5D42A992AA0 /* IQTextFieldViewInfoModel.swift */,
				B18E9E42F6DB3D1A4662C0E6B0948B20 /* IQTextFieldViewListener.swift */,
				4C89B730732A92E19E9E9B580E5A5B74 /* IQTextView.swift */,
				4FD5C6E1EA88852FEF8628F34A4C0BCA /* IQTitleBarButtonItem.swift */,
				C729F981E162E4971A977737D1D01640 /* IQToolbar.swift */,
				B979BCAFEA5C9EC623AB08B15FFA21FD /* IQToolbarConfiguration.swift */,
				9B39734B699CD68B73BAE285F10E54CA /* IQToolbarPlaceholderConfiguration.swift */,
				F602DE22C562384F2CEF0AD121CB128E /* IQUICollectionView+Additions.swift */,
				A69D4E881ED3E3E0FB12176B8A7FF999 /* IQUIScrollView+Additions.swift */,
				DF330D9FBA848850DA54AB516A4C6A10 /* IQUITableView+Additions.swift */,
				20A2A456A86322ED11F6C9351576FC09 /* IQUITextFieldView+Additions.swift */,
				AA1522F9FAC30EA2DA62E7FF993CA86F /* IQUIView+Hierarchy.swift */,
				83D678BA791A5CB0D43B19C51A5614D4 /* IQUIView+IQKeyboardToolbar.swift */,
				C665C3EEBF85BE7F61EF12436F008782 /* IQUIView+IQKeyboardToolbarDeprecated.swift */,
				449692FFD3124EC6C5FD624C59F126ED /* IQUIViewController+Additions.swift */,
				1D2C2C81E06765F6672A624A00006108 /* MainActor+AssumeIsolated.swift */,
				931B256AE49B31B2919957A56690B478 /* UIImage+NextPrevious.swift */,
				3E356EBC242C1B70CC3B6A3540E33378 /* Resources */,
				F0FF0EB81816A399798E23318552AFDC /* Support Files */,
			);
			name = IQKeyboardManagerSwift;
			path = IQKeyboardManagerSwift;
			sourceTree = "<group>";
		};
		868BA02A93C193CD2334723F2BBE11C3 /* Resources */ = {
			isa = PBXGroup;
			children = (
				26AD26C46D7A9F0087E3AE4A72C9B2CF /* PrivacyInfo.xcprivacy */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		9D1DD54DA5EEC66B31159EE126CFB709 /* SnapKit */ = {
			isa = PBXGroup;
			children = (
				A06E7979FF01D8CE948AAEA07223CC27 /* Constraint.swift */,
				5471F27C9FC5A9F05558B10F1837DFA9 /* ConstraintAttributes.swift */,
				CC7C0E6517F0FAFB044A8E2CE21B3170 /* ConstraintConfig.swift */,
				DD964421905CF776EAB81204975A40F0 /* ConstraintConstantTarget.swift */,
				E213D89E47AE3679ADB1CB0AAEF3A8E1 /* ConstraintDescription.swift */,
				C287C539748BDC4A6E64E459102794E5 /* ConstraintDirectionalInsets.swift */,
				3FF12E391298A594153C38F5C9BC4E4A /* ConstraintDirectionalInsetTarget.swift */,
				1BB2E7DC6F46623C46A99E439E577A39 /* ConstraintDSL.swift */,
				F98E2C9457B2AB09489823F7F9460054 /* ConstraintInsets.swift */,
				459A45FEBCEBE298BEE465D480035D7A /* ConstraintInsetTarget.swift */,
				F0AB94E52BD76051BE55DFDB83A9F8C1 /* ConstraintItem.swift */,
				63AF321CEA03A02B1345A22B88B465CE /* ConstraintLayoutGuide.swift */,
				391FF331ABD38614D9F4F46AA92E2534 /* ConstraintLayoutGuide+Extensions.swift */,
				605546073AF43F60F673269621B44E0D /* ConstraintLayoutGuideDSL.swift */,
				71F751095AF6515098FA05AF42FBDEE3 /* ConstraintLayoutSupport.swift */,
				F00CA9C87938AE674D9C17133FD3E262 /* ConstraintLayoutSupportDSL.swift */,
				550887E8DA7F780888F21EE1C8CAE165 /* ConstraintMaker.swift */,
				5EEA492D88734A748646251DBAD2A522 /* ConstraintMakerEditable.swift */,
				83BA9AB4AA05C5B19779260C54A342F3 /* ConstraintMakerExtendable.swift */,
				4166CF971A8D112BE1944A55F52B6D39 /* ConstraintMakerFinalizable.swift */,
				5AEB23950B83A8D1002278B4EC108F39 /* ConstraintMakerPrioritizable.swift */,
				1E9D9651F6FF223B030D31C19E490762 /* ConstraintMakerRelatable.swift */,
				06C2F42F525AFD9989167107534D0413 /* ConstraintMakerRelatable+Extensions.swift */,
				2AB1B027EC52C740BC3F056B0F1F17D7 /* ConstraintMultiplierTarget.swift */,
				75C6F3B3F08C44B224062AFCA5FCB1FC /* ConstraintOffsetTarget.swift */,
				6C48CBC3A65804D80F45F4F857055A7F /* ConstraintPriority.swift */,
				1656B953ED727582B8BDB43BD21C7852 /* ConstraintPriorityTarget.swift */,
				CC183D8476C35E2EB6C99573DA05F3C8 /* ConstraintRelatableTarget.swift */,
				FB1A4E994B86267EE9CA5ACDD11BC44D /* ConstraintRelation.swift */,
				5CD1FC29720287740820EF2F7613FDCE /* ConstraintView.swift */,
				E03F894CAA5A9D681C103609D6AB734B /* ConstraintView+Extensions.swift */,
				8989A59ADC6A439686C9BE31BCC827A8 /* ConstraintViewDSL.swift */,
				23201A67174F0451961F2B74F5414EC8 /* Debugging.swift */,
				635FA142D44F8701C13DD235553B1E96 /* LayoutConstraint.swift */,
				C4851A7A1E59F6A375CD84C88A3C5C1E /* LayoutConstraintItem.swift */,
				38BD960225DE4E14F6D93E3FB658125A /* Typealiases.swift */,
				74ECFFDF4D695B75B4B0263391B6B673 /* UILayoutSupport+Extensions.swift */,
				868BA02A93C193CD2334723F2BBE11C3 /* Resources */,
				EB94D8BA09D51AF1B012F4C399BB8E85 /* Support Files */,
			);
			name = SnapKit;
			path = SnapKit;
			sourceTree = "<group>";
		};
		B30944E91190F3F6A23A143CCFA9B1B5 /* Products */ = {
			isa = PBXGroup;
			children = (
				D6CCB9D258E15D810E80DB9950E2B5AC /* AAInfographics */,
				A8E950A16D00F649C54FFB30F81D7842 /* IQKeyboardManagerSwift */,
				8D8069D3964814114ACEC3084C010B59 /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */,
				5F2E4452C65B1A0C240DD8CF7592F81A /* Pods-ChargeSpot */,
				979486118B3E90C08386079D57962701 /* SnapKit */,
				B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BA4F31F07263C99FC76E66D632A59F09 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				143C29E3BE492836E12A84EF2EC76B27 /* iOS */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				BA4F31F07263C99FC76E66D632A59F09 /* Frameworks */,
				E9048089BE159F5CB3B11B08882DC85C /* Pods */,
				B30944E91190F3F6A23A143CCFA9B1B5 /* Products */,
				CF2ACA836B9EFA46F6A06AD36733418D /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		CF2ACA836B9EFA46F6A06AD36733418D /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				2F68E75A0CB33B5A8D996D0B52A36ACB /* Pods-ChargeSpot */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		E48BFB869E3C8278844F14E297AE7C5B /* AAInfographics */ = {
			isa = PBXGroup;
			children = (
				7D9915F67AD1CD0832E475399DFDD1DE /* AAAnimation.swift */,
				994CAF88FFE5AF0B5EBA4224FD47E4A1 /* AAAxis.swift */,
				0916C40CF3EC3AB0C1049A7CC982EF11 /* AABoxplot.swift */,
				******************************** /* AABubbleLegend.swift */,
				FC90D5D73117BFE6B72AC241483DD5BB /* AAButtonTheme.swift */,
				3C515E0AC897C731289C54184A98A795 /* AAChart.swift */,
				E94478C0EE33FA6880675BA62034B30B /* AAChartModel.swift */,
				6EDB1CB2DE510F33C8DA683776CE5D2B /* AAChartView.swift */,
				C6E2D909636C86AC957D5CE04F4128D3 /* AAChartView+API.swift */,
				ACC2B8C0FDB7AF11990DCF1E194190C7 /* AAChartViewPluginProvider.swift */,
				4CBD502083BD422E7FE665105DBC7EBC /* AAColor.swift */,
				B2A5499462201BA55C0A3C3907CA420E /* AAColumn.swift */,
				1887E63997903AB44D3D64E36198E737 /* AACredits.swift */,
				E1F16131E80C86699226A8045B09BC93 /* AACrosshair.swift */,
				F5A02C54472B6F3EC32A0160D8A57200 /* AADataLabels.swift */,
				AEEFA30FBF9F7C8096B33C038336F6DF /* AAExtension.swift */,
				1BD61D84A1CFA78D05835E84979EF817 /* AAGradientColor.swift */,
				887C1E50DAF0A76C9BFC404C38931E73 /* AAGradientColor+DefaultThemes.swift */,
				B6E0CFA0636D5ED0DE045E6CA2A6E663 /* AAGradientColor+LinearGradient.swift */,
				B7408994E8B02F05DD4E5D2D1EA25EC2 /* AAGradientColor+RadialGradient.swift */,
				D65EF91816145FD72DA8295640A52596 /* AALabel.swift */,
				5A28CCEAE1A932C4DD4A20AFE65F2765 /* AALabels.swift */,
				F7D627836243B653A101A8465F04BDEC /* AALang.swift */,
				877EFDE461652310FC438FCCC3B5BD0F /* AALayoutAlgorithm.swift */,
				483D6FF787DF666ABAF5013EFAD42A13 /* AALegend.swift */,
				E53B5E444D65BD6CFA1F562F80DE610B /* AAMarker.swift */,
				C4E28C64704DBDA820F361F088CFAFE6 /* AAOptions.swift */,
				4AB6104F539C1671A460348EE54AB0A4 /* AAPackedbubble.swift */,
				E00481C8F4DAA1927620296FC1202DF8 /* AAPane.swift */,
				C5C0857160FC1F5CC0EDD7CE22DCFE87 /* AAPie.swift */,
				069A551D0B6200D07EB296DDC229FC6E /* AAPlotBandsElement.swift */,
				419CB0F7F66FEAF40DD0EADF5509659A /* AAPlotLinesElement.swift */,
				4FFD26A6443E6C39B3C83BA581406A1D /* AAPlotOptions.swift */,
				6EF0BE6555E9CAE20A713625296145A7 /* AAScatter.swift */,
				B8261D73E5486EBCF552FC35653D39F4 /* AAScrollablePlotArea.swift */,
				C7773C7D7B5BF5AC5B5FEBE20BAB79AA /* AASerializable.swift */,
				5F3514E3F21055E195A5942DA3F38AD5 /* AASeries.swift */,
				04A91AF5C89A203A66943E259030D56E /* AASeriesElement.swift */,
				71BB6C68136D7C7D29FDCAE97FA070DD /* AAStates.swift */,
				1C3D0C9E374DD4F56223D64BBE9D67C3 /* AAStyle.swift */,
				9106F9AB42732BB5BF87F44EE52534AA /* AASubtitle.swift */,
				5082FDFC8C0EC6822130911954A9F07B /* AATitle.swift */,
				063F91C01308FA71ED4E8B32BCB63DB4 /* AATooltip.swift */,
				5061C5644507E3A08CCA27AE872C6CF9 /* AAXAxis.swift */,
				77BB3CE04DFFC883C9616BAABFAAD824 /* AAYAxis.swift */,
				F5CB8ACCF4F8D71BCE60B87052B0B347 /* ProjectBundlePathLoader.swift */,
				EBCCCEB0F5523D79CA293424DBB72DC5 /* Resources */,
				5D3420AA52A5348BE32D92C2DCBA8764 /* Support Files */,
			);
			name = AAInfographics;
			path = AAInfographics;
			sourceTree = "<group>";
		};
		E9048089BE159F5CB3B11B08882DC85C /* Pods */ = {
			isa = PBXGroup;
			children = (
				E48BFB869E3C8278844F14E297AE7C5B /* AAInfographics */,
				6939E259D36A1ABA9A4127D56F88635F /* IQKeyboardManagerSwift */,
				9D1DD54DA5EEC66B31159EE126CFB709 /* SnapKit */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		EB94D8BA09D51AF1B012F4C399BB8E85 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				A1A8B150F5ED29E5F4CE59330F4AB35A /* ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist */,
				8129CBFC4A84C1656433BBCD0034DAEB /* SnapKit.modulemap */,
				379916AD4322B47E998E121FFA8A3116 /* SnapKit-dummy.m */,
				E25CF85AA5546863B9667D2439B13F5F /* SnapKit-Info.plist */,
				1EBF0AF33E9F6E31641B7D1BB684376F /* SnapKit-prefix.pch */,
				FA7712C6E6B40A90C46A2CC450F20E26 /* SnapKit-umbrella.h */,
				ABFFE1C03AE79E8EB45757FD3CDA33DD /* SnapKit.debug.xcconfig */,
				772D9587D0849D0DFF1C1EC45320D6AF /* SnapKit.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/SnapKit";
			sourceTree = "<group>";
		};
		EBCCCEB0F5523D79CA293424DBB72DC5 /* Resources */ = {
			isa = PBXGroup;
			children = (
				46F8A15F3E0475D30C5A6FAB8EBC3188 /* AAJSFiles.bundle */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		F0FF0EB81816A399798E23318552AFDC /* Support Files */ = {
			isa = PBXGroup;
			children = (
				87CD60B71058F794E6B3653B0E28DC18 /* IQKeyboardManagerSwift.modulemap */,
				A2302A35710DB980F5D0CD9A7D204ABF /* IQKeyboardManagerSwift-dummy.m */,
				91CA0E574C6CEC3FA180547E105DFC92 /* IQKeyboardManagerSwift-Info.plist */,
				E34BE6459764A97A2F8DBD0426DF42A2 /* IQKeyboardManagerSwift-prefix.pch */,
				32AF54A9F4796CD636751A693B4281C9 /* IQKeyboardManagerSwift-umbrella.h */,
				F65EC09FBD198C1555CDEDF55C597C17 /* IQKeyboardManagerSwift.debug.xcconfig */,
				208DA1D9940067EA989655C16E527AA9 /* IQKeyboardManagerSwift.release.xcconfig */,
				C3B70A3507AB387C4C19593B30AB18AC /* ResourceBundle-IQKeyboardManagerSwift-IQKeyboardManagerSwift-Info.plist */,
			);
			name = "Support Files";
			path = "../Target Support Files/IQKeyboardManagerSwift";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		22A6E0786BFF99C8AB52002834138EDD /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3EF4E67F7853C9EC6078213CACC5EAE5 /* Pods-ChargeSpot-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2B091D63CFDBA0D293AC6EF71BF554D0 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3A3D2697E544C969F5C736F404A1BB66 /* IQKeyboardManagerSwift-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5B1500FE995B9224E0AF0B42CE93C03B /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				57C4F6EFB30DDD14E960AC2D6B34F904 /* SnapKit-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F9A07214E553E4E24A61297849873F92 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				******************************** /* AAInfographics-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 80CE967CAA1D35721519F892BAF7A19B /* Build configuration list for PBXNativeTarget "SnapKit" */;
			buildPhases = (
				5B1500FE995B9224E0AF0B42CE93C03B /* Headers */,
				F7AC6792C89443C7B212A06E810BAB97 /* Sources */,
				33428AC36668E3ED52DB70316F843FB8 /* Frameworks */,
				1DEDF411E550D85A1218E1655456A9CD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				5EBF4FD1549F2F4C3941AF39B0F3C51F /* PBXTargetDependency */,
			);
			name = SnapKit;
			productName = SnapKit;
			productReference = 979486118B3E90C08386079D57962701 /* SnapKit */;
			productType = "com.apple.product-type.framework";
		};
		81AB6AA06BE8625CF139234A31B027FE /* AAInfographics */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2B0ADC8A5D956F58C0F317AF3DCD7B6A /* Build configuration list for PBXNativeTarget "AAInfographics" */;
			buildPhases = (
				F9A07214E553E4E24A61297849873F92 /* Headers */,
				97A30ADBBF6A936ADC56960019ED49B3 /* Sources */,
				79E123CE7C2BE00816E19CE19C99028D /* Frameworks */,
				84425CF1D06AAB924A07312122E7619A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AAInfographics;
			productName = AAInfographics;
			productReference = D6CCB9D258E15D810E80DB9950E2B5AC /* AAInfographics */;
			productType = "com.apple.product-type.framework";
		};
		8A8DB685241263AFDF5E6B20FE67B93A /* SnapKit-SnapKit_Privacy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 36405571DE305F271A08BD16191C7A0A /* Build configuration list for PBXNativeTarget "SnapKit-SnapKit_Privacy" */;
			buildPhases = (
				B133EE4786268C1B014591E0F826C7FE /* Sources */,
				880D9639347E9BEC56915425CDF9EF87 /* Frameworks */,
				3BB763059AFD8956C1A716D2442C0428 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "SnapKit-SnapKit_Privacy";
			productName = SnapKit_Privacy;
			productReference = B9DCB5EC0B1CDADD221717CADDF62359 /* SnapKit-SnapKit_Privacy */;
			productType = "com.apple.product-type.bundle";
		};
		982A68D37F5DCBC1FC1FDC0BB2F0EB8E /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8624449170052EE8391B8A9DA5DED319 /* Build configuration list for PBXNativeTarget "IQKeyboardManagerSwift-IQKeyboardManagerSwift" */;
			buildPhases = (
				C29311729E8EF30C2B4DE139B92A2846 /* Sources */,
				E3E5D4F7883FAA199D7DFED0B2313BEF /* Frameworks */,
				2AF4143FB953C4B428D7BFA86867DF52 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "IQKeyboardManagerSwift-IQKeyboardManagerSwift";
			productName = IQKeyboardManagerSwift;
			productReference = 8D8069D3964814114ACEC3084C010B59 /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */;
			productType = "com.apple.product-type.bundle";
		};
		A02A10E651F1B9055B1E8FC8C74AE656 /* Pods-ChargeSpot */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4A870B08D1B1680C242796F2C3BD54E0 /* Build configuration list for PBXNativeTarget "Pods-ChargeSpot" */;
			buildPhases = (
				22A6E0786BFF99C8AB52002834138EDD /* Headers */,
				0BD9B314A227B0BCB7A9A98E0ACDD752 /* Sources */,
				1750A41D019B093F9660121890B53E83 /* Frameworks */,
				B4D85C098DAB2661FC59F5C8800B4903 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1E24A1F5FF581A2A06DBCAE0E52204E0 /* PBXTargetDependency */,
				067AB547D3604D0A547AD8AEE588340B /* PBXTargetDependency */,
				17CB42EF591516CB3904CE1BB71E419D /* PBXTargetDependency */,
			);
			name = "Pods-ChargeSpot";
			productName = Pods_ChargeSpot;
			productReference = 5F2E4452C65B1A0C240DD8CF7592F81A /* Pods-ChargeSpot */;
			productType = "com.apple.product-type.framework";
		};
		B490E7485944099E16C9CBD79119D1D4 /* IQKeyboardManagerSwift */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 51448EA139E840FA83FCECC113A519FE /* Build configuration list for PBXNativeTarget "IQKeyboardManagerSwift" */;
			buildPhases = (
				2B091D63CFDBA0D293AC6EF71BF554D0 /* Headers */,
				BC6DF187A160622FB3D6013D127DA817 /* Sources */,
				A76B7572AD1CD0125BA8227332330DC9 /* Frameworks */,
				CCD7A20148BCEAF20B03FE2494D7BCEC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				74C13218FB4E1B9DE43776AFD3179C60 /* PBXTargetDependency */,
			);
			name = IQKeyboardManagerSwift;
			productName = IQKeyboardManagerSwift;
			productReference = A8E950A16D00F649C54FFB30F81D7842 /* IQKeyboardManagerSwift */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 16.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = B30944E91190F3F6A23A143CCFA9B1B5 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				81AB6AA06BE8625CF139234A31B027FE /* AAInfographics */,
				B490E7485944099E16C9CBD79119D1D4 /* IQKeyboardManagerSwift */,
				982A68D37F5DCBC1FC1FDC0BB2F0EB8E /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */,
				A02A10E651F1B9055B1E8FC8C74AE656 /* Pods-ChargeSpot */,
				19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */,
				8A8DB685241263AFDF5E6B20FE67B93A /* SnapKit-SnapKit_Privacy */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1DEDF411E550D85A1218E1655456A9CD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9E0045B41BFE697DB4ADE151228024D2 /* SnapKit-SnapKit_Privacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2AF4143FB953C4B428D7BFA86867DF52 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				ED700CEFC48D83F8402C1577ECB94BC2 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		3BB763059AFD8956C1A716D2442C0428 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B9603E75D71200041A34A925D6F183BF /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		84425CF1D06AAB924A07312122E7619A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C40219960F480E093C1155A69D951C2C /* AAJSFiles.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B4D85C098DAB2661FC59F5C8800B4903 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		CCD7A20148BCEAF20B03FE2494D7BCEC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E3FD0840B77157C01FF5D52E72B31664 /* IQKeyboardManagerSwift-IQKeyboardManagerSwift in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0BD9B314A227B0BCB7A9A98E0ACDD752 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				808A3D2BE172D93F862574233C07722C /* Pods-ChargeSpot-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97A30ADBBF6A936ADC56960019ED49B3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2C1207AB554753DD987E471DEA8E9DED /* AAAnimation.swift in Sources */,
				70A2FE54635EEBD101D5D79B01609C4D /* AAAxis.swift in Sources */,
				923CA1FDC87DDEA88E02067BECD83C78 /* AABoxplot.swift in Sources */,
				******************************** /* AABubbleLegend.swift in Sources */,
				261F1743BA33D3065C9FD7D3D7D85BED /* AAButtonTheme.swift in Sources */,
				90005505FB933A3047BE185F63A99FB3 /* AAChart.swift in Sources */,
				990BE6EFC195B60FCFD4BE108238E6D0 /* AAChartModel.swift in Sources */,
				0F2BF86610A292C81CEA284234E49055 /* AAChartView.swift in Sources */,
				21F8C522C6567324FEFF24831E3F4FA4 /* AAChartView+API.swift in Sources */,
				707F5D2C0DD51CE01C4BCD8ACAFDE4F4 /* AAChartViewPluginProvider.swift in Sources */,
				D86B9BE31D45BF36BE78559FBA4254E6 /* AAColor.swift in Sources */,
				B11B3A15203508AEF294D78D656AD8DA /* AAColumn.swift in Sources */,
				F98C041BD5C35C89C43DFBD4DF7EB712 /* AACredits.swift in Sources */,
				3E52BAFA9AECEF850B67B1E7C32650BF /* AACrosshair.swift in Sources */,
				353180A0F0DC4B61F2BDED206473D6F9 /* AADataLabels.swift in Sources */,
				E862E5D575BED4D94CE1D4F24D841192 /* AAExtension.swift in Sources */,
				23E7FD4F69645294729EB3839D524607 /* AAGradientColor.swift in Sources */,
				CCD99576883F2A882BE68EF947336D51 /* AAGradientColor+DefaultThemes.swift in Sources */,
				D8486770C951AD52186C9D105E060A60 /* AAGradientColor+LinearGradient.swift in Sources */,
				0895381789B493294369A187CF16097B /* AAGradientColor+RadialGradient.swift in Sources */,
				45459AF4B2FD64DD66FBA5889A811D9E /* AAInfographics-dummy.m in Sources */,
				6CBE440BE36788B5E56E7C6124B2455A /* AALabel.swift in Sources */,
				F52F7402A43AC9A8D04DEA11E1B802C6 /* AALabels.swift in Sources */,
				FF8555B8EC8BC27E2A2C22D5075BE646 /* AALang.swift in Sources */,
				B2B121881248EA5DF4A53AA03A6F40DE /* AALayoutAlgorithm.swift in Sources */,
				312C7FD0CFDC0D70C789F4107C37D627 /* AALegend.swift in Sources */,
				7C7D4E1F438258C4363547D5CD7826CE /* AAMarker.swift in Sources */,
				7F9F8BFE4A612F1EE0C0B1688185ED35 /* AAOptions.swift in Sources */,
				2F4337F8364DE531ADD9281A9F7CBA9F /* AAPackedbubble.swift in Sources */,
				48DF918490C82FE2849F2058784FCB79 /* AAPane.swift in Sources */,
				2CE3EDDFC3568BED3EBB743D36BD0A69 /* AAPie.swift in Sources */,
				0C7C78E9CA8DC47092FAF58E9FCA2A13 /* AAPlotBandsElement.swift in Sources */,
				858D141CDA8B7F21BCBE5CD8476F0F53 /* AAPlotLinesElement.swift in Sources */,
				28CF7A42811BA9B634621316DCAFFCC5 /* AAPlotOptions.swift in Sources */,
				356F2FE4EDACF39DAFA756E537ECD06B /* AAScatter.swift in Sources */,
				903685875CBD28FE7FBF09128948638E /* AAScrollablePlotArea.swift in Sources */,
				9BBD05F55C106CC86C80DA2DA985C2B3 /* AASerializable.swift in Sources */,
				4A84D7C091F340B22AAD8B88C1522160 /* AASeries.swift in Sources */,
				80E37F3FDB349D19B5DD572479F75315 /* AASeriesElement.swift in Sources */,
				1083DD9C24614A83BD0A843008DF366C /* AAStates.swift in Sources */,
				7359EA4AA9D0841330E2F010AD8F25A5 /* AAStyle.swift in Sources */,
				568C235842D9191E16FFB6BCC2DBF6ED /* AASubtitle.swift in Sources */,
				C46472E5D2A254C2EA873CEE3E9607D6 /* AATitle.swift in Sources */,
				513FF454FF96C8751781104F7A651A9B /* AATooltip.swift in Sources */,
				99B2134E59C2BAFCFD0EAD4E508007A1 /* AAXAxis.swift in Sources */,
				9963434D8659BEAE9B61E4E9E2A5C412 /* AAYAxis.swift in Sources */,
				7713C42CE43548B685917AA35DC638E9 /* ProjectBundlePathLoader.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B133EE4786268C1B014591E0F826C7FE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		BC6DF187A160622FB3D6013D127DA817 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BBF8220B30095D0EB91D4933B4769D9E /* IQActiveConfiguration.swift in Sources */,
				3CBB581828D3D4237FF0E318F3457FC9 /* IQBarButtonItem.swift in Sources */,
				FA0B149FAA1A1C60DA9B92EC2569520E /* IQBarButtonItemConfiguration.swift in Sources */,
				C3E307D86BC99500B675C38EDCBFB0AC /* IQInvocation.swift in Sources */,
				59E13CB1013047AB75A9B464B1D82C2F /* IQKeyboardConfiguration.swift in Sources */,
				3EEB67A2DC733DD8677E443D43BA9A5A /* IQKeyboardInfo.swift in Sources */,
				DC00471B9629D6BA4DECB8796D6532EC /* IQKeyboardListener.swift in Sources */,
				45B3D0DA40BE97096DE3CDECE622585E /* IQKeyboardManager.swift in Sources */,
				5D269576532F504523EAA39DDB1557F4 /* IQKeyboardManager+Debug.swift in Sources */,
				F86C621FA07210C325437A3D8FA7CC68 /* IQKeyboardManager+Deprecated.swift in Sources */,
				0DDF4216E3E48EF14B994DEAF828EEA2 /* IQKeyboardManager+Internal.swift in Sources */,
				6DD19D888A917365E4C36AFE31FA2C27 /* IQKeyboardManager+Position.swift in Sources */,
				E7E7C94378820F81492BCF0CB798D46C /* IQKeyboardManager+Toolbar.swift in Sources */,
				9A578D9CD4BEC7C1168A1D9820DE40A8 /* IQKeyboardManager+ToolbarActions.swift in Sources */,
				DDE98DAE6570A84BB1C07E14DA12C4D4 /* IQKeyboardManager+UIKeyboardNotification.swift in Sources */,
				8ECCBA8E91C632E4F56D0D7F40F135F9 /* IQKeyboardManager+UITextFieldViewNotification.swift in Sources */,
				6C08C17E6D1DB2B4D1D0669F59F09368 /* IQKeyboardManagerCompatible.swift in Sources */,
				F633379AD81BA605AB1B01385C4B9248 /* IQKeyboardManagerConstants.swift in Sources */,
				11B67C495111E3AA200F8E6852A73A55 /* IQKeyboardManagerSwift-dummy.m in Sources */,
				72E7B39F6D1F97F30C08E04D211D304E /* IQKeyboardReturnKeyHandler.swift in Sources */,
				C892112213688A3A4523CDD9F323998B /* IQKeyboardReturnKeyHandler+TextFieldDelegate.swift in Sources */,
				67FE77018A04D8691A906DECD6CFFCA2 /* IQKeyboardReturnKeyHandler+TextViewDelegate.swift in Sources */,
				A267DD79A7AAD3036D940CDE6249255B /* IQNSArray+Sort.swift in Sources */,
				EE49210BAAF30051C28298E265D3D659 /* IQPlaceholderable.swift in Sources */,
				00DE7AE1D2A7B7F94F93458DB7754809 /* IQPreviousNextView.swift in Sources */,
				AF022BD7571EF5BCBB1EF3F1FD8EB7DD /* IQRootControllerConfiguration.swift in Sources */,
				EB6131723350AB4DCB5AEDF242D458AC /* IQScrollViewConfiguration.swift in Sources */,
				FE4B49773ED7A97B84316CF892D5D455 /* IQTextFieldViewInfo.swift in Sources */,
				ABCE13FE54D5DEE5CD02838201676A92 /* IQTextFieldViewInfoModel.swift in Sources */,
				17850B3BCF62F3E9C80608BF8B16291A /* IQTextFieldViewListener.swift in Sources */,
				54D89FF142C65EDD590F8651A0CFCB6E /* IQTextView.swift in Sources */,
				68D7B750E799100042584648B0634DDD /* IQTitleBarButtonItem.swift in Sources */,
				D0C9D7424D667076070CE75180ECCD6B /* IQToolbar.swift in Sources */,
				595079712F4E905BEEC8C8EAA167EA03 /* IQToolbarConfiguration.swift in Sources */,
				742CBD9F365744155B66ADE0EC6CF94B /* IQToolbarPlaceholderConfiguration.swift in Sources */,
				E7095898E9F45BD384B5BF177B642AFD /* IQUICollectionView+Additions.swift in Sources */,
				57EA452F77A51C3654DBC92A9A2B771A /* IQUIScrollView+Additions.swift in Sources */,
				70364D5528E3495AA5DE40030C0852B8 /* IQUITableView+Additions.swift in Sources */,
				139F8FB4B5A5FA7A297976A723CDCC9A /* IQUITextFieldView+Additions.swift in Sources */,
				D4E8DE674C6B890FE9D56D40BA77DD15 /* IQUIView+Hierarchy.swift in Sources */,
				E5F8F6C107339F507114C3AB566ED585 /* IQUIView+IQKeyboardToolbar.swift in Sources */,
				83A2615D5A90DC223EA13831DFA4CE13 /* IQUIView+IQKeyboardToolbarDeprecated.swift in Sources */,
				A9D7688EFF99BED99A406255EBFCCC6D /* IQUIViewController+Additions.swift in Sources */,
				9AD8B9EFD0F8D7B60A5DC4359601ED60 /* MainActor+AssumeIsolated.swift in Sources */,
				1ACC61B072F4BB20E73C2F462A7E16FB /* UIImage+NextPrevious.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C29311729E8EF30C2B4DE139B92A2846 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F7AC6792C89443C7B212A06E810BAB97 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6E39129FC8643A70C276801FEF4C280D /* Constraint.swift in Sources */,
				0CA7A132ABE7018DE9295456732F38BB /* ConstraintAttributes.swift in Sources */,
				F9EBA65892D78A31C068D727D84BCB88 /* ConstraintConfig.swift in Sources */,
				CE593943A9E7CF83822CF60304BCAD43 /* ConstraintConstantTarget.swift in Sources */,
				7AF516B98D45391B909D507D0244104C /* ConstraintDescription.swift in Sources */,
				1194E62AA3F6F506799B1A43B16942B5 /* ConstraintDirectionalInsets.swift in Sources */,
				E37671A03B4C17A1CF3766A6125833BB /* ConstraintDirectionalInsetTarget.swift in Sources */,
				868A9F524A7985BDA1EA124D9BF4CA63 /* ConstraintDSL.swift in Sources */,
				AF760C78F1C7E11BF7CB9E9B29903530 /* ConstraintInsets.swift in Sources */,
				2B2EB369550CE92CEEFCBFD3D32B8A3F /* ConstraintInsetTarget.swift in Sources */,
				5922A6A0AE7152CF436356B3556F1835 /* ConstraintItem.swift in Sources */,
				59F34874DA4ABB2F5C4E09EA6865936B /* ConstraintLayoutGuide.swift in Sources */,
				E3D779DEE753C0B0D33BA8E73A980265 /* ConstraintLayoutGuide+Extensions.swift in Sources */,
				3D3B646B4988314275B40E97BEB16C7F /* ConstraintLayoutGuideDSL.swift in Sources */,
				B0875E3AB8718E7DFE5C53497C02A15E /* ConstraintLayoutSupport.swift in Sources */,
				064D909CD827405E8DCC309DB1B7775A /* ConstraintLayoutSupportDSL.swift in Sources */,
				4F4DEB687C0E4834A5B291DEE0651D6A /* ConstraintMaker.swift in Sources */,
				C14F10B663FE2898EACAB90C202B3F50 /* ConstraintMakerEditable.swift in Sources */,
				D4218DA55B2BA45937589200CC0DF1FB /* ConstraintMakerExtendable.swift in Sources */,
				B903049E7C1BED7918DAB208754107C7 /* ConstraintMakerFinalizable.swift in Sources */,
				AABEF13464BA7F4621BD94736C1D057C /* ConstraintMakerPrioritizable.swift in Sources */,
				BDA5C7CC91E86448237CF40954FAC5AF /* ConstraintMakerRelatable.swift in Sources */,
				8BABA32F7B94A25D8E9208C0A8D90B2E /* ConstraintMakerRelatable+Extensions.swift in Sources */,
				883EDEE1C699497CF2A77C3B8A32A790 /* ConstraintMultiplierTarget.swift in Sources */,
				3577F172FA68CBAE47CFEE6FE25C5404 /* ConstraintOffsetTarget.swift in Sources */,
				09E1F569A93FAD4B9149E30B9301F44A /* ConstraintPriority.swift in Sources */,
				DBA4803F4765E1650B8C6841157F5D73 /* ConstraintPriorityTarget.swift in Sources */,
				C07CB3E9A4D1BF00F841E4285629A2B2 /* ConstraintRelatableTarget.swift in Sources */,
				ECC5C2ADC2682F9171FEA22AF10DCE53 /* ConstraintRelation.swift in Sources */,
				86CAB01D950C8BC35EDE0BDC01A2500B /* ConstraintView.swift in Sources */,
				0DE5DB9C6227B3416778D8417DD95EA9 /* ConstraintView+Extensions.swift in Sources */,
				7D42390CDB4FA147504B03DA2A174A0C /* ConstraintViewDSL.swift in Sources */,
				AE224EDB6D044C0FE86B086E950FC2F9 /* Debugging.swift in Sources */,
				BF1AE4D97E813B95C43EA4A298B973D1 /* LayoutConstraint.swift in Sources */,
				C6F45595676957ADBEC18EB3F23EAEC4 /* LayoutConstraintItem.swift in Sources */,
				BA2FB695DEB0D179253EEB8DFCE3578B /* SnapKit-dummy.m in Sources */,
				C6A4302ACE006C4E2CDD481287E2916B /* Typealiases.swift in Sources */,
				F6F33E8B268F3D41075374D95B8088DC /* UILayoutSupport+Extensions.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		067AB547D3604D0A547AD8AEE588340B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = IQKeyboardManagerSwift;
			target = B490E7485944099E16C9CBD79119D1D4 /* IQKeyboardManagerSwift */;
			targetProxy = 2D29126EC89554F7FE8ABDCC5B9DBFCE /* PBXContainerItemProxy */;
		};
		17CB42EF591516CB3904CE1BB71E419D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SnapKit;
			target = 19622742EBA51E823D6DAE3F8CDBFAD4 /* SnapKit */;
			targetProxy = 1AB9E2480ACC120E07ED306C5EB149F7 /* PBXContainerItemProxy */;
		};
		1E24A1F5FF581A2A06DBCAE0E52204E0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = AAInfographics;
			target = 81AB6AA06BE8625CF139234A31B027FE /* AAInfographics */;
			targetProxy = 6BF5813A60840FBE5026C0AB52432683 /* PBXContainerItemProxy */;
		};
		5EBF4FD1549F2F4C3941AF39B0F3C51F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "SnapKit-SnapKit_Privacy";
			target = 8A8DB685241263AFDF5E6B20FE67B93A /* SnapKit-SnapKit_Privacy */;
			targetProxy = 54C4CA56E598FDE9175DA0EED227E80F /* PBXContainerItemProxy */;
		};
		74C13218FB4E1B9DE43776AFD3179C60 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "IQKeyboardManagerSwift-IQKeyboardManagerSwift";
			target = 982A68D37F5DCBC1FC1FDC0BB2F0EB8E /* IQKeyboardManagerSwift-IQKeyboardManagerSwift */;
			targetProxy = 665EEFD184B51C531244B39D9C836DFA /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		22EF300A1E4EBBE1F7990D68B8654E86 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F65EC09FBD198C1555CDEDF55C597C17 /* IQKeyboardManagerSwift.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardManagerSwift";
				IBSC_MODULE = IQKeyboardManagerSwift;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardManagerSwift/ResourceBundle-IQKeyboardManagerSwift-IQKeyboardManagerSwift-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardManagerSwift;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		2638ADC880C60CCC8055137F4D883DF4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 208DA1D9940067EA989655C16E527AA9 /* IQKeyboardManagerSwift.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/IQKeyboardManagerSwift";
				IBSC_MODULE = IQKeyboardManagerSwift;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardManagerSwift/ResourceBundle-IQKeyboardManagerSwift-IQKeyboardManagerSwift-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				PRODUCT_NAME = IQKeyboardManagerSwift;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		40BEBCDE6A4522288D2476B3D583D3B7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ABFFE1C03AE79E8EB45757FD3CDA33DD /* SnapKit.debug.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SnapKit";
				IBSC_MODULE = SnapKit;
				INFOPLIST_FILE = "Target Support Files/SnapKit/ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = SnapKit_Privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Debug;
		};
		50A7EF466D3E7B0A6B79305B1B4BA788 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 2DAE27E2B3E5B122C991DE3A66BAEC84 /* Pods-ChargeSpot.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-ChargeSpot/Pods-ChargeSpot-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-ChargeSpot/Pods-ChargeSpot.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		531AB9B51913EE274022D67EB63CB4E1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		89B8688B66C09BA698AA1E786EA8FCEE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 208DA1D9940067EA989655C16E527AA9 /* IQKeyboardManagerSwift.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardManagerSwift/IQKeyboardManagerSwift-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardManagerSwift/IQKeyboardManagerSwift-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardManagerSwift/IQKeyboardManagerSwift.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardManagerSwift;
				PRODUCT_NAME = IQKeyboardManagerSwift;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		9D0D7572FEDA4CC9E75E7387846AFE57 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ABFFE1C03AE79E8EB45757FD3CDA33DD /* SnapKit.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SnapKit/SnapKit-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SnapKit/SnapKit-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SnapKit/SnapKit.modulemap";
				PRODUCT_MODULE_NAME = SnapKit;
				PRODUCT_NAME = SnapKit;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		A06A0B8B8C2AC1F57A3C67ACD4FEC0B3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		A0A3678230B9EA510F082AF1CF277AC8 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 63F2E7A289EE6CAE91B493D8878EE1B6 /* AAInfographics.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AAInfographics/AAInfographics-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AAInfographics/AAInfographics-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AAInfographics/AAInfographics.modulemap";
				PRODUCT_MODULE_NAME = AAInfographics;
				PRODUCT_NAME = AAInfographics;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		BAD20131EC29650C6737E66854A3A9FD /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 772D9587D0849D0DFF1C1EC45320D6AF /* SnapKit.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/SnapKit/SnapKit-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/SnapKit/SnapKit-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/SnapKit/SnapKit.modulemap";
				PRODUCT_MODULE_NAME = SnapKit;
				PRODUCT_NAME = SnapKit;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		C19176D272F6631EEA7F05F8A38697C7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F65EC09FBD198C1555CDEDF55C597C17 /* IQKeyboardManagerSwift.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/IQKeyboardManagerSwift/IQKeyboardManagerSwift-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/IQKeyboardManagerSwift/IQKeyboardManagerSwift-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/IQKeyboardManagerSwift/IQKeyboardManagerSwift.modulemap";
				PRODUCT_MODULE_NAME = IQKeyboardManagerSwift;
				PRODUCT_NAME = IQKeyboardManagerSwift;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.9;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		D7ADE148338D33E21C7342ADAD58CCEC /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 79B80460DD5D31C4F123D9DCDC0CDB28 /* Pods-ChargeSpot.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				INFOPLIST_FILE = "Target Support Files/Pods-ChargeSpot/Pods-ChargeSpot-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-ChargeSpot/Pods-ChargeSpot.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		EEC9257118C61CD8D68F59A35DCE1CB6 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 772D9587D0849D0DFF1C1EC45320D6AF /* SnapKit.release.xcconfig */;
			buildSettings = {
				CODE_SIGNING_ALLOWED = NO;
				CONFIGURATION_BUILD_DIR = "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/SnapKit";
				IBSC_MODULE = SnapKit;
				INFOPLIST_FILE = "Target Support Files/SnapKit/ResourceBundle-SnapKit_Privacy-SnapKit-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = SnapKit_Privacy;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				WRAPPER_EXTENSION = bundle;
			};
			name = Release;
		};
		F43E79A2FA08ECFA68C501315413DC06 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 47E7C221D88EA41368E545A8174DD365 /* AAInfographics.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/AAInfographics/AAInfographics-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "Target Support Files/AAInfographics/AAInfographics-Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MODULEMAP_FILE = "Target Support Files/AAInfographics/AAInfographics.modulemap";
				PRODUCT_MODULE_NAME = AAInfographics;
				PRODUCT_NAME = AAInfographics;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2B0ADC8A5D956F58C0F317AF3DCD7B6A /* Build configuration list for PBXNativeTarget "AAInfographics" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F43E79A2FA08ECFA68C501315413DC06 /* Debug */,
				A0A3678230B9EA510F082AF1CF277AC8 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		36405571DE305F271A08BD16191C7A0A /* Build configuration list for PBXNativeTarget "SnapKit-SnapKit_Privacy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				40BEBCDE6A4522288D2476B3D583D3B7 /* Debug */,
				EEC9257118C61CD8D68F59A35DCE1CB6 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A06A0B8B8C2AC1F57A3C67ACD4FEC0B3 /* Debug */,
				531AB9B51913EE274022D67EB63CB4E1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4A870B08D1B1680C242796F2C3BD54E0 /* Build configuration list for PBXNativeTarget "Pods-ChargeSpot" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				50A7EF466D3E7B0A6B79305B1B4BA788 /* Debug */,
				D7ADE148338D33E21C7342ADAD58CCEC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		51448EA139E840FA83FCECC113A519FE /* Build configuration list for PBXNativeTarget "IQKeyboardManagerSwift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C19176D272F6631EEA7F05F8A38697C7 /* Debug */,
				89B8688B66C09BA698AA1E786EA8FCEE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		80CE967CAA1D35721519F892BAF7A19B /* Build configuration list for PBXNativeTarget "SnapKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9D0D7572FEDA4CC9E75E7387846AFE57 /* Debug */,
				BAD20131EC29650C6737E66854A3A9FD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8624449170052EE8391B8A9DA5DED319 /* Build configuration list for PBXNativeTarget "IQKeyboardManagerSwift-IQKeyboardManagerSwift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22EF300A1E4EBBE1F7990D68B8654E86 /* Debug */,
				2638ADC880C60CCC8055137F4D883DF4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
