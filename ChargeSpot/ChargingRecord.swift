//
//  ChargingRecord.swift
//  ChargeSpot
//
//  Created by jj on 2025/5/31.
//

import Foundation

// MARK: - Enums

enum ChargingType: String, CaseIterable, Codable {
    case dcFast = "直流快充"
    case acSlow = "交流慢充"
    case unknown = "无法判断"
    
    var displayName: String {
        return self.rawValue
    }
}

// MARK: - ChargingRecord Model

struct ChargingRecord: Codable, Identifiable {
    let id: UUID
    var startTime: Date
    var endTime: Date
    var location: String
    var chargingType: ChargingType
    var startBatteryLevel: Int // 0-100
    var endBatteryLevel: Int // 0-100
    var cost: Double? // Optional charging cost
    var hasWaiting: Bool
    var waitingDuration: TimeInterval? // In seconds
    var rating: Int // 1-5 stars
    var tags: [String] // Text labels like "车位难找", "充得快", "有遮挡"
    var notes: String? // Additional notes
    var createdAt: Date
    var updatedAt: Date
    
    init(
        startTime: Date = Date(),
        endTime: Date = Date(),
        location: String = "",
        chargingType: ChargingType = .unknown,
        startBatteryLevel: Int = 0,
        endBatteryLevel: Int = 0,
        cost: Double? = nil,
        hasWaiting: Bool = false,
        waitingDuration: TimeInterval? = nil,
        rating: Int = 5,
        tags: [String] = [],
        notes: String? = nil
    ) {
        self.id = UUID()
        self.startTime = startTime
        self.endTime = endTime
        self.location = location
        self.chargingType = chargingType
        self.startBatteryLevel = startBatteryLevel
        self.endBatteryLevel = endBatteryLevel
        self.cost = cost
        self.hasWaiting = hasWaiting
        self.waitingDuration = waitingDuration
        self.rating = rating
        self.tags = tags
        self.notes = notes
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    // MARK: - Computed Properties
    
    var chargingDuration: TimeInterval {
        return endTime.timeIntervalSince(startTime)
    }
    
    var batteryGain: Int {
        return endBatteryLevel - startBatteryLevel
    }
    
    var formattedDuration: String {
        let hours = Int(chargingDuration) / 3600
        let minutes = Int(chargingDuration) % 3600 / 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
    
    var formattedWaitingDuration: String? {
        guard let waitingDuration = waitingDuration else { return nil }
        
        let hours = Int(waitingDuration) / 3600
        let minutes = Int(waitingDuration) % 3600 / 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
    
    var formattedCost: String? {
        guard let cost = cost else { return nil }
        return String(format: "¥%.2f", cost)
    }
    
    // MARK: - Mutating Methods
    
    mutating func updateRecord() {
        self.updatedAt = Date()
    }
}

// MARK: - Sample Data

extension ChargingRecord {
    static var sampleData: [ChargingRecord] {
        let now = Date()
        let calendar = Calendar.current
        
        return [
            ChargingRecord(
                startTime: calendar.date(byAdding: .day, value: -2, to: now) ?? now,
                endTime: calendar.date(byAdding: .hour, value: -46, to: now) ?? now,
                location: "万达广场地下停车场 B2-15",
                chargingType: .dcFast,
                startBatteryLevel: 25,
                endBatteryLevel: 85,
                cost: 45.60,
                hasWaiting: true,
                waitingDuration: 900, // 15 minutes
                rating: 4,
                tags: ["充得快", "车位难找"],
                notes: "周末人多，等了一会儿"
            ),
            ChargingRecord(
                startTime: calendar.date(byAdding: .day, value: -5, to: now) ?? now,
                endTime: calendar.date(byAdding: .hour, value: -116, to: now) ?? now,
                location: "小区充电桩 A区03号",
                chargingType: .acSlow,
                startBatteryLevel: 15,
                endBatteryLevel: 100,
                cost: 28.50,
                hasWaiting: false,
                rating: 5,
                tags: ["有遮挡", "安全"],
                notes: "小区内充电很方便"
            ),
            ChargingRecord(
                startTime: calendar.date(byAdding: .day, value: -7, to: now) ?? now,
                endTime: calendar.date(byAdding: .hour, value: -167, to: now) ?? now,
                location: "特来电充电站 CBD店",
                chargingType: .dcFast,
                startBatteryLevel: 30,
                endBatteryLevel: 90,
                cost: 52.80,
                hasWaiting: false,
                rating: 3,
                tags: ["价格贵"],
                notes: "充电速度一般，价格偏高"
            )
        ]
    }
}
